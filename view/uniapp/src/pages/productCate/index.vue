<template>
    <tig-layout>
        <!-- 自定义导航栏 -->
        <view class="custom-navbar">
            <view class="status-bar"></view>
            <view class="navbar-content">
                <view class="navbar-left">
                    <image 
                        src="/static/images/common/search.svg" 
                        class="search-icon"
                        mode="aspectFit"
                        @click="toSearch"
                    />
                </view>
                <view class="logo-text">SANKUWA</view>
                <view class="navbar-right"></view>
            </view>
        </view>

        <!-- 横幅列表 -->
        <view class="banner-container" :style="containerStyle">
            <scroll-view
                scroll-y="true"
                class="banner-scroll"
                refresher-enabled="true"
                :refresher-triggered="refreshing"
                @refresherrefresh="onRefresh"
                @refresherrestore="onRestore"
                :enable-back-to-top="true"
                :scroll-with-animation="true"
            >
                <view v-if="loading" class="loading-wrapper">
                    <view class="loading-spinner"></view>
                    <text class="loading-text">加载中...</text>
                </view>
                
                <view v-else class="banner-list">
                    <view 
                        v-for="(category, index) in categoryList" 
                        :key="category.categoryId"
                        class="banner-item"
                        @click="navigateToSecondLevel(category)"
                    >
                        <view class="banner-wrapper">
                            <image 
                                :src="category.bannerImage || category.categoryPic" 
                                class="banner-image"
                                mode="aspectFill"
                                :lazy-load="true"
                                @error="handleImageError"
                            />
                            <view class="banner-overlay">
                                <view class="category-name-container">
                                    <text class="category-name">{{ category.categoryName }}</text>
                                </view>
                            </view>
                        </view>
                        <view v-if="!category.bannerImage && !category.categoryPic" class="banner-placeholder">
                            <text class="placeholder-text">{{ category.categoryName }}</text>
                        </view>
                    </view>
                </view>

                <!-- 空状态 -->
                <view v-if="!loading && categoryList.length === 0" class="empty-state">
                    <text class="empty-text">暂无分类数据</text>
                    <view class="retry-btn" @click="fetchCategoryData">
                        <text class="retry-text">点击重试</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </tig-layout>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { getCategoryAll } from "@/api/productCate/productCate";
import { useConfigStore } from "@/store/config";
import { useTabbarStore } from "@/store/tabbar";

const configStore = useConfigStore();
const tabbarStore = useTabbarStore();

interface Category {
    categoryId: number;
    categoryName: string;
    categoryPic?: string;
    bannerImage?: string;
    children?: Category[];
}

const categoryList = ref<Category[]>([]);
const loading = ref(true);
const refreshing = ref(false);

// 计算容器样式
const containerStyle = computed(() => {
    const navbarHeight = 130; // 导航栏高度
    const statusBarHeight = configStore.safeAreaInsets.top || 44;
    const tabbarHeight = tabbarStore.currentActiveValue > -1 ? 50 : 0;
    const totalNavHeight = navbarHeight + statusBarHeight;

    return {
        paddingTop: `${totalNavHeight}rpx`,
        paddingBottom: `${tabbarHeight}rpx`,
        height: `calc(100vh - ${totalNavHeight}rpx - ${tabbarHeight}rpx)`
    };
});

// 获取分类数据
const fetchCategoryData = async () => {
    try {
        if (!refreshing.value) {
            loading.value = true;
        }
        const result = await getCategoryAll();
        
        // 这里可以添加横幅图片数据，如果API没有提供bannerImage字段
        // 可以临时使用一些示例图片或者categoryPic
        categoryList.value = (result || []).map((item: any) => ({
            ...item,
            bannerImage: item.bannerImage || item.categoryPic // 使用横幅图片或分类图片
        }));
        
    } catch (error) {
        console.error('获取分类数据失败:', error);
        uni.showToast({
            title: '加载失败，请重试',
            icon: 'none'
        });
    } finally {
        loading.value = false;
    }
};

// 跳转到搜索页面
const toSearch = () => {
    uni.navigateTo({
        url: "/pages/search/index"
    });
};

// 跳转到二级页面
const navigateToSecondLevel = (category: Category) => {
    uni.navigateTo({
        url: `/pages/productCate/secondLevel?categoryId=${category.categoryId}&categoryName=${encodeURIComponent(category.categoryName)}`
    });
};

// 图片加载失败处理
const handleImageError = (event: any) => {
    console.warn('横幅图片加载失败:', event);
};

// 下拉刷新
const onRefresh = () => {
    refreshing.value = true;
    fetchCategoryData().finally(() => {
        refreshing.value = false;
    });
};

// 刷新结束
const onRestore = () => {
    refreshing.value = false;
};

onMounted(() => {
    fetchCategoryData();
});
</script>

<style lang="scss" scoped>
/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
}

.status-bar {
    height: var(--status-bar-height);
    background: #fff;
}

.navbar-content {
    height: 180rpx;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0 40rpx 40rpx 40rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
}

.navbar-left,
.navbar-right {
    width: 100rpx;
    display: flex;
    justify-content: center;
}

.logo-text {
            font-size: 48rpx;
            font-weight: 600;
            color: #333;
            letter-spacing: 2rpx;
            position: relative;
            padding: 0 20rpx;
            position: relative;
            top: -27rpx;
            
            &::after {
                content: '';
                position: absolute;
                bottom: -8rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 80%;
                height: 4rpx;
                background: linear-gradient(to right, transparent, #333, transparent);
                border-radius: 2rpx;
            }
        }

.search-icon {
    width: 46rpx;
    height: 46rpx;
    opacity: 0.7;
    transition: all 0.3s ease;
    position: relative;
    top: -33rpx;
    
    &:active {
        opacity: 1;
        transform: scale(0.9);
    }
}

/* 横幅容器 */
.banner-container {
    background: #f5f5f5;
    position: relative;
    overflow: hidden;
}

.banner-scroll {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

/* 加载状态 */
.loading-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 0;
}

.loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 横幅列表 */
.banner-list {
    padding: 20rpx 0;
}

.banner-item {
    margin: 0 20rpx 20rpx 20rpx;
    overflow: hidden;
    background: #fff;
    cursor: pointer;
    transition: transform 0.2s ease;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
    
    &:active {
        transform: scale(0.98);
    }
    
    &:last-child {
        margin-bottom: 0;
    }
}

.banner-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
    overflow: hidden;
}

.banner-image {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-name-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.category-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.6);
    border-bottom: 4rpx solid #fff;
    padding-bottom: 8rpx;
    letter-spacing: 1rpx;
    line-height: 1.2;
    text-align: center;
}

/* 占位符样式 */
.banner-placeholder {
    width: 100%;
    height: 420rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 200rpx 0;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
}

.retry-btn {
    padding: 20rpx 40rpx;
    background: #f0f0f0;
    border-radius: 8rpx;
    transition: background-color 0.2s ease;
    
    &:active {
        background: #e0e0e0;
    }
}

.retry-text {
    font-size: 26rpx;
    color: #666;
}

/* 兼容小程序 */
page {
    background-color: #f5f5f5 !important;
}
</style>
