<!-- 首页 -->
<template>
    <tig-layout>
        <template v-if="configStore.previewId > 0">
            <previewTip></previewTip>
        </template>

        <template v-if="loading">
            <view class="index_empty">
                <image lazy-load :src="staticResource('common/index_empty.png')" mode="widthFix"></image>
            </view>
        </template>
        <template v-else>
            <view class="index">
                
                <!-- 头部区域：Logo + 搜索框 -->
                <view class="header-section">
                    <view class="logo-container">
                        <text class="logo-text">SANKUWA</text>
                    </view>
                    <view class="search-container">
                        <view class="search-input" @click="goToSearch">
                            <image class="search-icon" src="/static/images/common/search.svg" mode="aspectFit"></image>
                            <text class="search-placeholder">当季新品</text>
                        </view>
                    </view>
                </view>

                <!-- 平台公告消息 -->
                <view class="promo-text-section">
                    <text class="promo-text" :class="{ 'text-fade': isTextChanging }">{{ promoText }}</text>
                    <image class="announcement-icon" src="/static/images/common/<EMAIL>" mode="aspectFit"></image>
                </view>
                       
               

                <!-- 广告位轮播图 -->
                <view class="banner-section">
                    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="4000" duration="800" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
                        <swiper-item v-for="(banner, index) in bannerList" :key="index">
                            <view class="banner-item">
                                <image lazy-load :src="banner.image || ''" mode="aspectFill" class="banner-image" @error="onImageError"></image>
                                <!-- 图片名称 -->
                                <!-- <view class="banner-overlay"></view>
                                <view class="banner-content" v-if="banner.title">
                                    <text class="banner-title">{{ banner.title }}</text>
                                </view> -->
                            </view>
                        </swiper-item>
                    </swiper>
                </view>

                <!-- 分类导航 -->
                <view class="category-nav-wrapper">
                    <scroll-view class="category-nav" 
                                scroll-x="true" 
                                show-scrollbar="false">
                        <view class="nav-content">
                            <view v-for="(category, index) in categoryList" 
                                  :key="category.categoryId"
                                  class="nav-item" 
                                  :id="'nav-item-' + index"
                                  :class="{ active: activeCategory === index }"
                                  @click="switchCategory(index, category.categoryId)">
                                <text class="nav-text">{{ category.categoryName }}</text>
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <!-- 商品推荐区域 -->
                <view v-if="!loading && (heroProduct || productCarouselList.length > 0)" class="product-section">
                    <!-- 数据加载中提示 -->
                    <view v-if="!isDataInitialized" class="loading-placeholder">
                        <view class="loading-text">正在加载商品数据...</view>
                    </view>
                    <!-- 商品特色展示区域 -->
                    <view class="product-showcase" :class="{ switching: isProductSwitching, 'fade-in': isProductFadeIn }">
                        <!-- 商品大图片 -->
                        <view v-if="heroProduct" class="product-hero" @click="navigateToProduct(heroProduct)">
                            <image
                                lazy-load
                                :key="heroProduct?.categoryId + '-' + activeCategory"
                                :src="currentCategoryPic"
                                mode="aspectFill"
                                class="product-hero-image"
                                @load="onImageLoad"
                                @error="onImageError"
                            ></image>
                            <view class="product-hero-content">
                            </view>
                        </view>
                        
                        <!-- 商品轮播图 -->
                        <view v-if="productCarouselList.length > 0" class="product-carousel">
                            <swiper 
                                class="product-swiper" 
                                :indicator-dots="true"
                                :autoplay="true" 
                                :interval="3500" 
                                :duration="600"
                                indicator-color="rgba(255, 255, 255, 0.3)"
                                indicator-active-color="rgba(255, 255, 255, 0.8)"
                            >
                                <swiper-item v-for="(item, index) in productCarouselList" :key="index">
                                    <view class="carousel-item" @click="navigateToProduct(item)">
                                        <image 
                                            lazy-load 
                                            :src="item.image" 
                                            mode="aspectFill" 
                                            class="carousel-image"
                                            @error="onCarouselImageError"
                                        ></image>
                                    </view>
                                </swiper-item>
                            </swiper>
                        </view>
                    </view>
                    
                    <!-- 商品列表 -->
                    <scroll-view class="product-scroll" scroll-x="true" show-scrollbar="false">
                        <view class="product-list" :class="{ switching: isProductSwitching, 'fade-in': isProductFadeIn }">
                            <view class="product-item" v-for="(product, index) in productList" :key="index" @click="onProductClick(product)">
                                <view class="product-image-wrapper">
                                    <image lazy-load :src="product.image" mode="aspectFill" class="product-image" @error="onImageError"></image>
                                    <view class="product-tag" v-if="product.tag">{{ product.tag }}</view>
                                </view>
                                <view class="product-info">
                                    <text class="product-name">{{ product.name  }}</text>
                                    <text class="product-description">{{ product.description  }}</text>
                                    <view class="product-price">
                                        <text class="price-symbol">¥</text>
                                        <text class="price-value">{{ product.price  }}</text>
                                    </view>
                                </view>
                            </view>
                            <!-- 左滑查看更多提示 -->
                            <view class="more-hint-item" @click="onMoreHintClick">
                                <view class="more-hint-content">
                                    <text class="hint-text">左滑查看更多</text>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>
                
                <!-- 数据加载失败或无数据时的显示 -->
                <view v-else-if="!loading && isDataInitialized && !heroProduct && productCarouselList.length === 0" class="no-data-section">
                    <view class="no-data-content">
                        <text class="no-data-text">暂无商品数据</text>
                        <button class="retry-button" @click="getIndexData">重新加载</button>
                    </view>
                </view>

                
                <!-- 本季新品推荐 -->
                <view class="season-new-section">
                    <text class="season-new-title">本季新品推荐</text>
                </view>

                <!-- 可展开图片轮播区域 -->
                <view class="expandable-carousel-section">
                    <view class="carousel-container">
                                                <!-- 左右滑动手势区域 -->
                        <view class="swipe-area" 
                              @touchstart="onTouchStart" 
                              @touchend="onTouchEnd">
                            <view class="carousel-panels">
                                <view 
                                    class="panel-item" 
                                    :class="{ 
                                        active: activePanel === index
                                    }"
                                    v-for="(panel, index) in carouselPanels" 
                                    :key="index"
                                    @click="onPanelClick(panel, index)"
                                >
                                    <image lazy-load :src="panel.image" mode="aspectFill" class="panel-image" @error="onImageError"></image>
                                    <view class="panel-overlay"></view>
                                    
                                    <view class="panel-content">
                                        <text class="panel-title">{{ panel.title }}</text>
                                        <text class="panel-subtitle" @click.stop="onCarouselImageClick(panel)">{{ panel.subtitle }}</text>
                                    </view>
                                    
                                </view>
                            </view>
                        </view>
                        
                        <!-- 底部指示器 -->
                        <view class="carousel-indicators">
                            <view 
                                class="indicator" 
                                :class="{ 
                                    active: activePanel === index
                                }"
                                v-for="(panel, index) in carouselPanels" 
                                :key="index"
                                @click="switchPanel(index)"
                            >
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 户外经典商品区域 -->
                <view class="outdoor-classics-section">
                    <view class="section-title-container">
                        <text class="section-title">户外经典</text>
                    </view>
                    
                    <scroll-view class="outdoor-product-scroll" scroll-x="true" show-scrollbar="false">
                        <view class="outdoor-product-list">
                            <view class="outdoor-product-item" v-for="(product, index) in outdoorProducts" :key="index" @click="onProductClick(product)">
                                <view class="outdoor-product-image-wrapper">
                                    <image lazy-load :src="product.image" mode="aspectFill" class="outdoor-product-image" @error="onImageError"></image>
                                </view>
                                <view class="outdoor-product-info">
                                    <text class="outdoor-product-name">{{ product.name }}</text>
                                    <text class="outdoor-product-code">{{ product.code }}</text>
                                    <view class="outdoor-product-price">
                                        <text class="outdoor-price-symbol">¥</text>
                                        <text class="outdoor-price-value">{{ product.price }}</text>
                                    </view>
                                </view>
                            </view>
                            <!-- 左滑查看更多提示 -->
                            <view class="outdoor-more-hint-item" @click="onOutdoorMoreHintClick">
                                <view class="outdoor-more-hint-content">
                                    <text class="outdoor-hint-text">左滑查看更多</text>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <!-- SANKUWA 品牌介绍区域 -->
                <view class="brand-section">
                    <view class="brand-background">
                        <image src="@/static/images/logo/0165etbypv1cekyakwmcsp3632.png" mode="aspectFill" class="brand-bg-image" @error="onImageError"></image>
                        <view class="brand-overlay"></view>
                        <view class="brand-content">
                            <text class="brand-logo">SANKUWA</text>
                            <text class="brand-slogan">探索自然 · 定义生活</text>
                            <text class="brand-description">
                                SANKUWA致力于为现代探索者打造专属装备，融合都市时尚与户外功能。我们严选优质面料，精工制作，每一件产品都经过严格品质把控。
                                
                                品牌承诺7天无理由退换，全国包邮服务，专业客服团队为您提供贴心服务。已获得100万+用户信赖，覆盖全国50+城市，提供1000+精品单品选择。
                            </text>
                            <view class="copyright">
                                <text class="copyright-text">© 2025 安徽乐联网络科技有限公司 版权所有</text>
                                <text class="icp-text" @click="openIcpLink">皖ICP备2025076484号-3</text>
                            </view>
                        </view>
                    </view>
                </view>
             
            </view>
        </template>

        <!-- 悬浮活动入口 -->
        <view class="floating-activity-entrance"
              :class="{
                  'entrance-hover': isEntranceHovered,
                  'entrance-clicked': isEntranceClicked
              }"
              @click="onActivityClick">
            <image
                class="activity-image"
                src="/static/images/activity/<EMAIL>"
                mode="aspectFit"
                @error="onImageError">
            </image>
        </view>

        <tig-back-top :scrollTop="scrollTop"></tig-back-top>
    </tig-layout>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad, onReachBottom, onPullDownRefresh, onShow, onHide, onShareAppMessage, onShareTimeline, onUnload, onPageScroll } from "@dcloudio/uni-app";
import { getIndex, getTopAds } from "@/api/home/<USER>";
import { getTopArticle } from "@/api/article/article";
import previewTip from "./src/previewTip.vue";
import { getCateProduct } from "@/api/home/<USER>";
import { getCategoryAll, getCategoryList } from "@/api/productCate/productCate";
import type { GetProductFilterResult } from "@/types/home/<USER>";
import { useConfigStore } from "@/store/config";
import { usecatnavStore } from "@/store/catnav";
import { staticResource } from "@/utils";

// 创建一个响应式的图片预加载函数
const ensureImageLoaded = (src: string): Promise<string> => {
    return new Promise((resolve) => {
        if (!src) {
            resolve('/static/images/common/placeholder.jpg');
            return;
        }
        
        // 如果图片已在缓存中，直接返回
        if (imageCache.value.has(src)) {
            resolve(src);
            return;
        }
        
        // 如果正在加载中，等待加载完成
        if (imageLoadingStates.value.get(src)) {
            const checkInterval = setInterval(() => {
                if (imageCache.value.has(src) || !imageLoadingStates.value.get(src)) {
                    clearInterval(checkInterval);
                    resolve(imageCache.value.has(src) ? src : '/static/images/common/placeholder.jpg');
                }
            }, 50);
            return;
        }
        
        // 开始加载图片
        imageLoadingStates.value.set(src, true);
        
        const img = new Image();
        img.onload = () => {
            imageCache.value.add(src);
            imageLoadingStates.value.set(src, false);
            resolve(src);
        };
        img.onerror = () => {
            imageLoadingStates.value.set(src, false);
        };
        
        // 设置超时机制 - 减少到3秒
        setTimeout(() => {
            if (imageLoadingStates.value.get(src)) {
                imageLoadingStates.value.set(src, false);
            }
        }, 3000);
        
        img.src = src;
    });
};

// 批量预加载图片
const preloadImages = async (imageUrls: string[]) => {
    const uniqueUrls = [...new Set(imageUrls.filter(url => url && !imageCache.value.has(url)))];
    
    if (uniqueUrls.length === 0) return;
    
    // 并发加载图片，但限制并发数
    const concurrencyLimit = 3;
    const promises: Promise<void>[] = [];
    
    for (let i = 0; i < uniqueUrls.length; i += concurrencyLimit) {
        const batch = uniqueUrls.slice(i, i + concurrencyLimit);
        const batchPromises = batch.map(async (url) => {
            try {
                await ensureImageLoaded(url);
            } catch (error) {
                // 忽略单个图片加载失败
            }
        });
        promises.push(Promise.all(batchPromises) as any);
    }
    
    await Promise.all(promises);
};
import { useScrollTop } from "@/hooks";

const { scrollTop } = useScrollTop(onPageScroll);

const configStore = useConfigStore();

// 跳转到搜索页面
const goToSearch = () => {
    uni.navigateTo({
        url: '/pages/search/index'
    });
};

// 打开ICP备案查询链接
const openIcpLink = () => {
    // #ifdef H5
    window.open('https://beian.miit.gov.cn/#/Integrated/recordQuery', '_blank');
    // #endif

    // #ifdef MP-WEIXIN || APP-PLUS
    uni.showModal({
        title: '提示',
        content: '请在浏览器中访问：https://beian.miit.gov.cn/#/Integrated/recordQuery',
        showCancel: false,
        confirmText: '知道了'
    });
    // #endif
};

// 图片加载处理
const onImageLoad = (e: any) => {
    // 图片加载完成
};

const onImageError = (e: any) => {
    // 图片加载失败处理
};

const onCarouselImageError = (e: any) => {
    // 轮播图片加载失败处理
};


// 跳转到商品详情或分类页面
const navigateToProduct = (item: any) => {
    if (item && item.type === 'hotSecondCategory' && item.categoryId) {
        // 热门二级分类：跳转到分类页面
        uni.navigateTo({
            url: `/pages/productCate/secondLevel?categoryId=${item.categoryId}`
        });
    } else if (item && item.type === 'parentCategory' && item.categoryId) {
        // 一级分类：跳转到分类页面
        uni.navigateTo({
            url: `/pages/productCate/secondLevel?categoryId=${item.categoryId}`
        });
    } else if (item && item.type === 'product' && item.productId) {
        // 商品：跳转到商品详情页面
        uni.navigateTo({
            url: `/pages/product/index?id=${item.productId}`
        });
    }
};

const showCatNav = ref(0);
const loading = ref(false);
const modulesData = ref<any[]>([]);
const currentCategoryId = ref(0);
const page = ref(1);
const loadend = ref(false);
const bottomLoading = ref(true);
const commodityList = ref<GetProductFilterResult[]>([]);

// 公告文字
const promoText = ref('SANKUWA 恭候您的尊贵莅临，愿您在此邂逅心仪臻品');
const promoArticles = ref<any[]>([]);
const currentPromoIndex = ref(0);
const isTextChanging = ref(false);
let promoTimer: any = null;

// 轮播图数据 - 使用本地占位图片提高加载速度
const bannerList = ref<Array<{
    picId?: number;
    picUrl?: string;
    picName?: string;
    pic_thumb?: string;
    image?: string;
    title?: string;
    subtitle?: string;
    description?: string;
}>>([
    {},{}, {}
]);

// 分类导航数据
const categoryList = ref<any[]>([]);

// 当前激活的分类
const activeCategory = ref(0);

// 可展开轮播相关
const activePanel = ref(0);

const carouselPanels = ref<any[]>([]);

// 获取广告位数据
const getBannerData = async () => {
    try {
        const result = await getTopAds();
        if (result && Array.isArray(result)) {
            bannerList.value = result.map((item: any) => ({
                picId: item.picId,
                picUrl: item.picUrl,
                picName: item.picName,
                pic_thumb: item.pic_thumb,
                image: item.picUrl || item.pic_thumb, // 优先使用 picUrl，降级使用 pic_thumb
                title: item.picName || '广告位',
                subtitle: '',
                description: ''
            }));
        }
    } catch (error) {
        console.error('获取广告位数据失败:', error);
    }
};

// 获取平台消息文字内容
const getPromoText = async () => {
    try {
        const result = await getTopArticle();
        
        if (result && Array.isArray(result) && result.length > 0) {
            // 存储所有文章数据
            promoArticles.value = result;
            currentPromoIndex.value = 0;
            // 开始轮播
            startPromoRotation();
        } else {
            console.log('⚠️ 平台消息接口返回数据格式不正确:', result);
        }
    } catch (error: any) {
        console.error('❌ 获取平台消息文字失败:', error);
        console.error('错误详情:', error.message, error.stack);
        // 保留默认文字作为降级方案
    }
};

// 开始公告文字轮播
const startPromoRotation = () => {
    // 清除之前的定时器
    if (promoTimer) {
        clearInterval(promoTimer);
        promoTimer = null;
    }
    
    // 如果只有一条公告，不需要轮播
    if (promoArticles.value.length <= 1) {
        return;
    }
    
    // 每7秒切换一次
    promoTimer = setInterval(() => {
        // 开始淡出动画
        isTextChanging.value = true;
        
        setTimeout(() => {
            // 切换文字内容
            currentPromoIndex.value = (currentPromoIndex.value + 1) % promoArticles.value.length;
            promoText.value = promoArticles.value[currentPromoIndex.value].articleTitle ;
            
            // 结束动画，开始淡入
            setTimeout(() => {
                isTextChanging.value = false;
            }, 70);
        }, 200);
    }, 5000);
};

// 停止公告文字轮播
const stopPromoRotation = () => {
    if (promoTimer) {
        clearInterval(promoTimer);
        promoTimer = null;
    }
};

// 加载新品轮播数据
const loadNewProductPanels = async () => {
    try {
        console.log('开始加载新品轮播数据...');
        const response = await getCateProduct({
            page: 1,
            size: 4,
            isNew: 1  // 只获取新品
        });
        
        console.log('新品API响应:', response);
        
        if (response?.records) {
            // 过滤并只保留isNew为1的商品
            const newProducts = response.records.filter(product => product.isNew === 1);
            
            carouselPanels.value = newProducts.map(product => ({
                title: product.productName ,
                subtitle: '立即选购',
                image: product.picThumb,
                productSn: product.productSn,
                productId: product.productId  // 保留productId用于其他用途
            }));
        }
    } catch (error) {
        console.error('加载新品数据失败:', error);
    }
};

// 商品列表切换动画控制
const isProductSwitching = ref(false);

const isProductFadeIn = ref(false);

// 商品轮播图数据
const productCarouselList = ref<any[]>([]);

// 大图片商品数据
const heroProduct = ref<any>(null);

// 数据初始化状态
const isDataInitialized = ref(false);

// 初始化失败重试次数
const retryCount = ref(0);
const maxRetries = 3;

// 分类数据缓存标识
const isCategoryDataLoaded = ref(false);

// 首页基础数据缓存标识
const isHomeDataLoaded = ref(false);

// 商品数据缓存 - 按分类ID缓存
const productCache = ref<Map<number, any[]>>(new Map());

// 图片缓存和加载状态
const imageCache = ref<Set<string>>(new Set());
const imageLoadingStates = ref<Map<string, boolean>>(new Map());

// 悬浮活动入口相关状态
const isEntranceHovered = ref(false);
const isEntranceClicked = ref(false);
let entranceTimer: any = null;


// 当前分类图片URL（计算属性，确保响应式更新）
const currentCategoryPic = computed(() => {
    const pic = heroProduct.value?.categoryPic;
    return pic || '/static/placeholder.jpg';
});

// 户外经典商品数据 - 从后端接口获取数据
const outdoorProducts = computed(() => {
    // 获取最多6个推荐商品用于户外经典区域展示
    const bestProducts = commodityList.value.filter(product => product.isBest === 1);
    const products = bestProducts.length > 0 ? bestProducts : commodityList.value.slice(0, 6);
    
    const result = products.slice(0, 6).map(product => {
        const productId = product.productId;
        if (!productId) {
            console.warn('outdoorProducts computed: 发现商品数据缺少productId:', product);
        }
        
        return {
            name: product.productName || '商品名称',
            code: product.productSn || '商品编号',
            price: product.productPrice ? Number(product.productPrice).toFixed(2) : '0.00',
            image: product.picThumb || '/static/images/common/placeholder.jpg',
            productId: productId
        };
    });
    
    console.log('outdoorProducts computed: 户外商品数据', result.length, '个商品');
    return result;
});



// 当前显示的商品列表 - 从后端接口获取数据
const productList = computed(() => {
    // 将商品数据转换为组件需要的格式
    const products = commodityList.value.map(product => {
        const productId = product.productId;
        if (!productId) {
            console.warn('productList computed: 发现商品数据缺少productId:', product);
        }
        
        return {
            name: product.productName || '商品名称',
            description: product.productSn || '商品编号',  // 使用产品编号作为描述
            price: product.productPrice ? Number(product.productPrice).toFixed(2) : '0.00',
            image: product.picThumb || '/static/placeholder.jpg',
            tag: product.isHot === 1 ? '热门' : (product.isNew === 1 ? '新品' : (product.isBest === 1 ? '推荐' : '')),
            productId: productId
        };
    });
    
    console.log('productList computed: 商品列表数据', products.length, '个商品');
    return products;
});



// 切换分类
const switchCategory = async (index: number, categoryId?: number) => {
    if (activeCategory.value === index) return;
    
    const categoryName = categoryList.value[index]?.categoryName ;
    
    // 立即更新激活状态，让导航栏样式快速响应
    activeCategory.value = index;
    
    // 开始切换动画
    isProductSwitching.value = true;
    isProductFadeIn.value = false;
    
    // 直接调用API加载数据
    if (categoryId) {
        // 先加载轮播图数据（会自动选择最合适的三级分类）
        await loadProductCarouselData(categoryId, index);
        
        // 使用轮播图选择的三级分类ID来加载主商品列表
        const finalCategoryId = heroProduct.value?.categoryId || categoryId;
        loadGoodsList(finalCategoryId);
    } else {
        // 如果没有categoryId，加载所有商品
        await loadProductCarouselData(undefined, index);
    }
    
    // 使用 requestAnimationFrame 优化动画性能
    requestAnimationFrame(() => {
        // 延迟切换数据，让淡出动画先执行
        setTimeout(() => {
            // productList 现在是 computed 属性，自动从 commodityList 获取数据
            // 不需要手动赋值，只需要确保 commodityList 有正确的数据
            
            // 切换数据后，开始淡入动画
            requestAnimationFrame(() => {
                isProductSwitching.value = false;
                isProductFadeIn.value = true;
                
                // 淡入动画完成后重置状态
                setTimeout(() => {
                    isProductFadeIn.value = false;
                }, 600);
            });
        }, 200);
    });
};

// 切换轮播面板
const switchPanel = (index: number) => {
    activePanel.value = index;
};

// 处理轮播面板点击事件 - 区分切换和跳转
const onPanelClick = (panel: any, index: number) => {
    
    // 如果点击的是当前激活的面板，则跳转到商品详情
    if (activePanel.value === index) {
        onCarouselImageClick(panel);
    } else {
        // 否则切换到该面板
        switchPanel(index);
    }
};

// 轮播图片点击事件 - 跳转到商品详情
const onCarouselImageClick = (panel: any) => {
    
    if (!panel || !panel.productId) {
        uni.showToast({
            title: '商品信息无效',
            icon: 'none'
        });
        return;
    }
    
    const productId = panel.productId;
    
    // 跳转到商品详情页，使用productId作为参数
    uni.navigateTo({
        url: `/pages/product/index?id=${productId}`,
        success: () => {
        },
        fail: (err) => {
            uni.showToast({
                title: '跳转失败',
                icon: 'none'
            });
        }
    });
};

// 触摸滑动处理
let touchStartX = 0;
let touchEndX = 0;

const onTouchStart = (e: any) => {
    touchStartX = e.touches[0].clientX;
};

const onTouchEnd = (e: any) => {
    touchEndX = e.changedTouches[0].clientX;
    handleSwipeGesture();
};

const handleSwipeGesture = () => {
    const swipeThreshold = 50; // 最小滑动距离
    const diff = touchStartX - touchEndX;
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // 向左滑动，显示下一张
            goToNext();
        } else {
            // 向右滑动，显示上一张
            goToPrevious();
        }
    }
};

const goToNext = () => {
    const nextIndex = (activePanel.value + 1) % carouselPanels.value.length;
    switchPanel(nextIndex);
};

const goToPrevious = () => {
    const prevIndex = (activePanel.value - 1 + carouselPanels.value.length) % carouselPanels.value.length;
    switchPanel(prevIndex);
};

// 商品点击事件
const onProductClick = (product: any) => {
    
    if (!product) {
        uni.showToast({
            title: '商品数据错误',
            icon: 'none'
        });
        return;
    }
    
    const productId = product.productId;
    if (!productId || productId === '' || productId === 0) {
        uni.showToast({
            title: '商品ID无效',
            icon: 'none'
        });
        return;
    }
    
    // 确保productId是数字
    const numericId = Number(productId);
    if (isNaN(numericId) || numericId <= 0) {
        uni.showToast({
            title: '商品ID格式错误',
            icon: 'none'
        });
        return;
    }
    
    // 跳转到商品详情页
    uni.navigateTo({
        url: `/pages/product/index?id=${numericId}`
    });
};

// 主商品区域左滑查看更多点击事件
const onMoreHintClick = () => {
    // 获取当前一级分类ID
    const firstCategoryId = getFirstCategoryId();
    
    if (firstCategoryId) {
        uni.navigateTo({
            url: `/pages/productCate/secondLevel?categoryId=${firstCategoryId}`
        });
    } else {
        uni.showToast({
            title: '暂无分类信息',
            icon: 'none'
        });
    }
};

// 户外经典区域左滑查看更多点击事件
const onOutdoorMoreHintClick = () => {
    // 获取当前一级分类ID
    const firstCategoryId = getFirstCategoryId();
    
    if (firstCategoryId) {
        uni.navigateTo({
            url: `/pages/productCate/secondLevel?categoryId=${firstCategoryId}`
        });
    } else {
        uni.showToast({
            title: '暂无分类信息',
            icon: 'none'
        });
    }
};

// 获取当前一级分类ID的辅助函数
const getFirstCategoryId = () => {
    if (categoryList.value.length > 0 && activeCategory.value >= 0) {
        const currentCategory = categoryList.value[activeCategory.value];
        return currentCategory.categoryId || currentCategory.id;
    }
    return null;
};



const onActivityClick = () => {

    // 如果还没有完全展示，第一次点击展示完整入口
    if (!isEntranceHovered.value) {

        // 清除之前的定时器
        if (entranceTimer) {
            clearTimeout(entranceTimer);
            entranceTimer = null;
        }

        // 展示完整入口
        isEntranceHovered.value = true;

        // 触发点击反馈动画
        isEntranceClicked.value = true;
        setTimeout(() => {
            isEntranceClicked.value = false;
        }, 300);

        // 设置较长的显示时间，给用户足够时间进行第二次点击
        entranceTimer = setTimeout(() => {
            isEntranceHovered.value = false;
        }, 2500); // 2.5秒后自动隐藏

        return; // 第一次点击不跳转
    }

    // 第二次点击：跳转页面

    // 触发点击动画
    isEntranceClicked.value = true;

    // 等待点击动画效果结束后再跳转
    setTimeout(() => {
        // 重置点击状态
        isEntranceClicked.value = false;

        uni.navigateTo({
            url: '/pages/activity/index',
            success: () => {
            },
            fail: (err) => {
                uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                });
            }
        });
    }, 300); // 等待300ms，让点击动画效果完成
};

const loadGoodsList = (categoryId: number) => {
    currentCategoryId.value = categoryId;
    if (currentCategoryId.value === 0) return;
    page.value = 1;
    loadend.value = false;
    commodityList.value = [];
    getProductList();
};

//获取首页分类商品列表
const getProductList = async () => {
    bottomLoading.value = true;
    try {
        // 确保参数有效，修复参数名称
        const params: any = {
            cat: currentCategoryId.value,  // 使用 cat 而不是 categoryId
            page: page.value,
            size: 20  // 添加页面大小
        };
        
        const result = await getCateProduct(params);
        
        if (result.records && result.records.length > 0) {
            // 验证每个商品的数据完整性
            const validProducts = result.records.filter(product => {
                const productId = product.productId;
                if (!productId || productId === 0) {
                    return false;
                }
                return true;
            });
            
            // 如果是第一页，直接替换数据；如果是后续页，则追加数据
            if (page.value === 1) {
                commodityList.value = validProducts;
            } else {
                commodityList.value = [...commodityList.value, ...validProducts];
            }
            loadend.value = validProducts.length === 0;
        } else {    
            loadend.value = true;
            if (page.value === 1) {
                commodityList.value = [];
            }
        }
    } catch (error: any) {
        // 获取商品列表失败
        if (page.value === 1) {
            commodityList.value = [];
        }
        uni.showToast({
            title: error.message || '获取商品列表失败',
            icon: 'none'
        });
    } finally {
        bottomLoading.value = false;
    }
};

// 加载分类数据
const loadCategoryData = async () => {
    try {
        // 如果分类数据已经加载过，直接使用缓存
        if (!isCategoryDataLoaded.value || categoryList.value.length === 0) {
        const result = await getCategoryAll();
        categoryList.value = result || [];
            isCategoryDataLoaded.value = true;
        }
        
        // 如果有分类数据，默认选择第一个并加载其商品数据
        if (categoryList.value.length > 0) {
            activeCategory.value = 0;
            // 加载第一个分类的商品数据
            const firstCategory = categoryList.value[0];
            const firstCategoryId = firstCategory.categoryId || firstCategory.id;
            
            if (firstCategoryId) {
                // 先加载轮播图数据（会自动选择最合适的三级分类）
                await loadProductCarouselData(firstCategoryId, 0);
                // 使用轮播图选择的三级分类ID来加载主商品列表
                const finalCategoryId = heroProduct.value?.categoryId || firstCategoryId;
                loadGoodsList(finalCategoryId);
            }
        } else {
            uni.showToast({
                title: '暂无分类数据',
                icon: 'none'
            });
        }
    } catch (error) {
        uni.showToast({
            title: '加载分类失败',
            icon: 'none'
        });
        throw error;
    }
};

// 从已缓存的分类数据中获取指定一级分类的children字段中的二级分类
const getSubCategories = (parentCategoryId: number) => {
    try {
        // 直接使用已经加载的分类数据，避免重复调用接口
        const categories = Array.isArray(categoryList.value) ? categoryList.value : [];
        
        // 查找当前一级分类
        const currentCategory = categories.find((cat: any) => 
            cat.categoryId === parentCategoryId || cat.id === parentCategoryId
        );
        
        // 从当前一级分类的children字段中获取二级分类
        const subCategories = currentCategory?.children || [];
        
        return subCategories;
    } catch (error) {
        return [];
    }
};

// 查找热门二级分类，然后找到对应的三级分类用于图片展示
const findHotSecondCategory = (subCategories: any[]) => {
    // 1. 筛选出所有isHot为1的热门分类
    const hotCategories = subCategories.filter((cat: any) => cat.isHot === 1);
    
    if (hotCategories.length > 0) {
        // 2. 按sortOrder字段排序（升序，数字越小排序越靠前）
        const sortedHotCategories = hotCategories.sort((a: any, b: any) => {
            const sortA = a.sortOrder || 999;
            const sortB = b.sortOrder || 999;
            return sortA - sortB;
        });
        
        const selectedHotSecondCategory = sortedHotCategories[0];
        
        // 3. 从选中的热门二级分类中查找三级分类
        return findThirdLevelCategory(selectedHotSecondCategory);
    }
    
    // 4. 如果没有热门分类，选择sortOrder最小的分类，然后查找其三级分类
    if (subCategories.length > 0) {
        const sortedCategories = [...subCategories].sort((a: any, b: any) => {
            const sortA = a.sortOrder || 999;
            const sortB = b.sortOrder || 999;
            return sortA - sortB;
        });
        
        const selectedSecondCategory = sortedCategories[0];
        
        // 查找该二级分类的三级分类
        return findThirdLevelCategory(selectedSecondCategory);
    }
    
    return null;
};

// 从二级分类中查找最适合的三级分类用于图片展示
const findThirdLevelCategory = (secondCategory: any) => {
    const thirdCategories = secondCategory.children || [];
    
    if (thirdCategories.length === 0) {
        // 如果没有三级分类，返回二级分类，但添加父级信息
        return {
            ...secondCategory,
            parentId: secondCategory.parentId || secondCategory.parentCategoryId,
            isSecondLevel: true
        };
    }
    
    // 1. 筛选出所有isHot为1的热门三级分类
    const hotThirdCategories = thirdCategories.filter((cat: any) => cat.isHot === 1);
    
    if (hotThirdCategories.length > 0) {
        // 2. 按sortOrder字段排序（升序，数字越小排序越靠前）
        const sortedHotThirdCategories = hotThirdCategories.sort((a: any, b: any) => {
            const sortA = a.sortOrder || 999;
            const sortB = b.sortOrder || 999;
            return sortA - sortB;
        });
        
        const selectedThirdCategory = sortedHotThirdCategories[0];
        
        // 添加父级分类信息
        return {
            ...selectedThirdCategory,
            parentId: secondCategory.categoryId || secondCategory.id,
            parentSecondCategoryId: secondCategory.categoryId || secondCategory.id,
            isThirdLevel: true
        };
    }
    
    // 3. 如果没有热门三级分类，选择sortOrder最小的三级分类
    const sortedThirdCategories = [...thirdCategories].sort((a: any, b: any) => {
        const sortA = a.sortOrder || 999;
        const sortB = b.sortOrder || 999;
            return sortA - sortB;
        });
        
    const selectedThirdCategory = sortedThirdCategories[0];
    
    // 添加父级分类信息
    return {
        ...selectedThirdCategory,
        parentId: secondCategory.categoryId || secondCategory.id,
        parentSecondCategoryId: secondCategory.categoryId || secondCategory.id,
        isThirdLevel: true
    };
};



// 加载分类数据和大图片数据
const loadProductCarouselData = async (categoryId?: number, categoryIndex?: number) => {
    try {
        let resultHeroProduct = null;
        let resultCarouselList: any[] = [];
        
        // 大图片逻辑：获取当前一级分类下的热门二级分类图片
        if (categoryIndex !== undefined && categoryList.value[categoryIndex]) {
            const currentCategory = categoryList.value[categoryIndex];
            const currentCategoryId = currentCategory.categoryId || currentCategory.id;
            
            // 获取当前一级分类下的二级分类
            const subCategories = getSubCategories(currentCategoryId);
            
            if (subCategories.length > 0) {
                // 查找热门二级分类，并获取对应的三级分类
                const finalCategory = findHotSecondCategory(subCategories);
                
                if (finalCategory) {
                    const categoryLevel = finalCategory.isThirdLevel ? '三级分类' : '二级分类';
                    
                    resultHeroProduct = {
                        categoryPic: finalCategory.categoryPic ,
                        categoryId: finalCategory.categoryId,
                        categoryName: finalCategory.categoryName,
                        parentCategoryId: currentCategoryId,
                        parentSecondCategoryId: finalCategory.parentSecondCategoryId,  // 保存二级分类ID作为备用
                        type: 'finalCategory',
                        categoryLevel: categoryLevel  // 记录分类层级
                    };
                    
                    // 调试信息：显示当前选择的分类层级
                    if (finalCategory.isThirdLevel) {
                    }
                }
            }
            
            // 如果没有找到合适的分类，回退到使用一级分类图片
            if (!resultHeroProduct) {
                resultHeroProduct = {
                    categoryPic: currentCategory.categoryPic ,
                    categoryId: currentCategoryId,
                    categoryName: currentCategory.categoryName,
                    type: 'parentCategory'
                };
            }
        }
        
        // 商品轮播图逻辑：根据三级分类ID获取商品数据
        const targetCategoryId = resultHeroProduct?.categoryId || categoryId;
        
        if (targetCategoryId) {
            // 检查缓存中是否已有该分类的商品数据
            if (productCache.value.has(targetCategoryId)) {
                resultCarouselList = productCache.value.get(targetCategoryId) || [];
            } else {
                // 缓存中没有，发起请求 - 使用三级分类ID获取商品数据
                const categoryLevel = (resultHeroProduct as any)?.categoryLevel || '未知层级';
                
        const params: any = {
            page: 1,
                    size: 4,
                    cat: targetCategoryId  // 这里是三级分类的ID
        };
        
        const result = await getCateProduct(params);
        
        if (result && result.records && result.records.length > 0) {
                    
            resultCarouselList = result.records.map((product: any) => ({
                image: product.picThumb ,
                productId: product.productId,
                productName: product.productName,
                        productPrice: product.productPrice,
                        type: 'product',
                        fromCategory: resultHeroProduct?.categoryName || '默认分类',
                        categoryId: targetCategoryId  // 保存分类ID用于跳转
                    }));
                    
                    // 将数据存入缓存
                    productCache.value.set(targetCategoryId, resultCarouselList);
                } else {
                    const fallbackCategoryId = (resultHeroProduct as any)?.parentSecondCategoryId || categoryId;
                    if (fallbackCategoryId && fallbackCategoryId !== targetCategoryId) {
                        const fallbackParams: any = {
                            page: 1,
                            size: 4,
                            cat: fallbackCategoryId
                        };
                        
                        const fallbackResult = await getCateProduct(fallbackParams);
                        
                        if (fallbackResult && fallbackResult.records && fallbackResult.records.length > 0) {
                            resultCarouselList = fallbackResult.records.map((product: any) => ({
                                image: product.picThumb ,
                                productId: product.productId,
                                productName: product.productName,
                                productPrice: product.productPrice,
                                type: 'product',
                                fromCategory: `${resultHeroProduct?.categoryName || '默认分类'}(备用)`,
                                categoryId: fallbackCategoryId,
                                isFromFallback: true  // 标记这是备用数据
                            }));
                            
                            // 将备用数据存入缓存
                            productCache.value.set(targetCategoryId, resultCarouselList);
                        } 
                    }
                }
            }
        }
        
        // 确保轮播图有4张图片
        while (resultCarouselList.length < 4) {
            resultCarouselList.push({
                productId: null,
                productName: '暂无商品',
                type: 'placeholder',
                fromCategory: '占位符'
            });
        }
        resultCarouselList = resultCarouselList.slice(0, 4);
        
        // 更新显示数据
        heroProduct.value = resultHeroProduct;
        productCarouselList.value = resultCarouselList;
        
        // 显示最终结果摘要
        const finalCategoryInfo = resultHeroProduct ? 
            `${resultHeroProduct.categoryName}(ID:${resultHeroProduct.categoryId})` : 
            '无分类数据';
        
    } catch (error) {
        heroProduct.value = null;
        productCarouselList.value = [];
    }
};

const getIndexData = async () => {
    loading.value = true;
    
    try {
        // 重置数据状态
        isDataInitialized.value = false;
        heroProduct.value = null;
        productCarouselList.value = [];
        
        // 只有在首页数据未加载时才调用接口
        if (!isHomeDataLoaded.value) {
        const res = await getIndex();
        modulesData.value = res.moduleList;
        showCatNav.value = 1;
            isHomeDataLoaded.value = true;
        }
        
        // 获取广告位数据
        await getBannerData();
        
        // 获取平台消息文字
        await getPromoText();
        
        uni.stopPullDownRefresh();
        
        // 加载分类数据（会自动加载第一个分类的商品数据）
        await loadCategoryData();
        
        // 预加载关键图片
        const imagesToPreload: string[] = [
            ...bannerList.value.map(banner => banner.image || '').filter(Boolean),
            ...carouselPanels.value.map(panel => panel.image || '').filter(Boolean),
            ...outdoorProducts.value.slice(0, 3).map(product => product.image || '').filter(Boolean), // 预加载前3个商品图片
            '/static/images/common/brand-bg.jpg'
        ];
        
        // 异步预加载，不阻塞页面初始化
        setTimeout(() => {
            preloadImages(imagesToPreload);
        }, 500);
        
        // 标记数据初始化完成
        isDataInitialized.value = true;
        retryCount.value = 0;
        
    } catch (error) {
        // 重试逻辑
        if (retryCount.value < maxRetries) {
            retryCount.value++;
            
            setTimeout(() => {
                getIndexData();
            }, 1000 * retryCount.value);
        } else {
            uni.showToast({
                title: '加载失败，请刷新重试',
                icon: 'none',
                duration: 3000
            });
        }
    } finally {
        loading.value = false;
    }
};

// 检查环境变量是否准备就绪
const checkEnvReady = () => {
    return new Promise((resolve) => {
        const apiUrl = import.meta.env.VITE_API_URL;
        const hasLocation = typeof location !== 'undefined';
        
        // 立即检查环境变量状态
        if (apiUrl) {
            resolve(true);
            return;
        }
        
        if (hasLocation) {
            resolve(true);
            return;
        }
        
        // 如果都没有，设置一个短暂的检查间隔
        const checkInterval = setInterval(() => {
            if (import.meta.env.VITE_API_URL || typeof location !== 'undefined') {
                clearInterval(checkInterval);
                resolve(true);
            }
        }, 100);
        
        // 最多等待1秒
        setTimeout(() => {
            clearInterval(checkInterval);
            resolve(true);
        }, 1000);
    });
};

onLoad(async (options: any) => {
    if (options && options.previewId) {
        configStore.previewId = options.previewId;
    }
    catnavStore.reset();
    
    // 等待环境变量准备就绪
    await checkEnvReady();
    
    // 延迟一小段时间确保页面完全初始化
    setTimeout(async () => {
        await Promise.all([
            getIndexData(),
            loadNewProductPanels()  // 加载新品轮播数据
        ]);
    }, 100);
});

onUnload(() => {
    catnavStore.reset();
    // 清理公告轮播定时器
    stopPromoRotation();
    // 清理悬浮入口定时器
    if (entranceTimer) {
        clearTimeout(entranceTimer);
        entranceTimer = null;
    }
});

const catnavStore = usecatnavStore();
onPullDownRefresh(() => {
    // 重置所有状态
    currentCategoryId.value = 0;
    activeCategory.value = 0;
    isDataInitialized.value = false;
    isCategoryDataLoaded.value = false; // 清除分类数据缓存标识
    isHomeDataLoaded.value = false; // 清除首页数据缓存标识
    productCache.value.clear(); // 清空商品缓存
    imageCache.value.clear(); // 清空图片缓存
    imageLoadingStates.value.clear(); // 清空图片加载状态
    retryCount.value = 0;
    heroProduct.value = null;
    productCarouselList.value = [];
    categoryList.value = []; // 清空分类数据
    
    // 重置store状态
    catnavStore.reset();
    
    // 重新加载数据
    Promise.all([
        getIndexData(),
        loadNewProductPanels()  // 刷新新品轮播数据
    ]);
});

onReachBottom(() => {
    if (currentCategoryId.value > 0 && !loading.value && !loadend.value) {
        page.value++;
        getProductList();
    }
});

onShow(() => {
    // 如果数据还没有初始化，重新加载
    if (!isDataInitialized.value && !loading.value) {
        getIndexData();
    }
    
    // 恢复公告轮播
    if (promoArticles.value.length > 1) {
        startPromoRotation();
    }
    
    if (scrollTop.value > 0) {
        setTimeout(() => {
            uni.pageScrollTo({
                scrollTop: scrollTop.value,
                duration: 0
            });
        }, 20);
    }
    uni.hideTabBar();
});

onHide(() => {
    // 页面隐藏时暂停公告轮播
    stopPromoRotation();
});

onShareAppMessage((res) => {
    return {
        title: configStore.baseInfo.shopTitle
    };
});
onShareTimeline(() => {
    return {
        title: configStore.baseInfo.shopTitle
    };
});
</script>
<style>
page {
    background-color: #ffffff !important;
}
</style>
<style lang="scss" scoped>
.index {
    background-color: #ffffff;
    min-height: 100vh;
    padding-top: 60rpx; /* 添加顶部间距，让整体页面往下移动 */
}

.index_empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;

    image {
        width: 400rpx;
    }
}

// 加载状态样式
.loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80rpx 0;
    
    .loading-text {
        font-size: 28rpx;
        color: #666;
    }
}


@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// 无数据状态样式
.no-data-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 120rpx 0;
    
    .no-data-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 40rpx;
        
        .no-data-text {
            font-size: 28rpx;
            color: #666;
        }
        
        .retry-button {
            padding: 20rpx 40rpx;
            background: #333;
            color: #fff;
            border-radius: 50rpx;
            font-size: 28rpx;
            border: none;
            
            &:active {
                opacity: 0.8;
            }
        }
    }
}

// 头部区域样式
.header-section {
    padding: 20rpx 30rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    
    .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20rpx 0 30rpx;
        
        .logo-text {
            font-size: 48rpx;
            font-weight: 600;
            color: #333;
            letter-spacing: 2rpx;
            position: relative;
            padding: 0 20rpx;
            
            &::after {
                content: '';
                position: absolute;
                bottom: -8rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 80%;
                height: 4rpx;
                background: linear-gradient(to right, transparent, #333, transparent);
                border-radius: 2rpx;
            }
        }
    }
    
    .search-container {
        width: 100%;
        
        .search-input {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 32rpx;
            padding: 16rpx 24rpx;
            width: 100%;
            
            .search-icon {
                width: 40rpx;
                height: 40rpx;
                margin-right: 16rpx;
            }
            
            .search-placeholder {
                color: #999;
                font-size: 28rpx;
            }
        }
    }
}

// 新增的文字区域样式
.promo-text-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx 30rpx;
    margin: 0 auto;
    width: fit-content;
}

.promo-text {
    font-size: 28rpx;
    color: #333;
    margin-right: 10rpx;
}
                
.announcement-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 8rpx;
}
                
.promo-text {
                    font-size: 24rpx;
    color: #697994;
                        font-weight: 500;
    transition: opacity 0.3s ease-in-out;
}

.promo-text.text-fade {
    opacity: 0.3;
}

// 轮播图样式
.banner-section {
    margin: 12rpx 30rpx 30rpx;
    
    .banner-swiper {
        height: 420rpx;
        overflow: hidden;
        
         // 轮播图指示器 - 长方形设计
         ::v-deep .uni-swiper-dot {
                    width: 80rpx !important;
                    height: 6rpx !important;
                    border-radius: 3rpx !important;
                    background-color: rgba(255,255,255,0.3) !important;
                    margin: 0 6rpx !important;
                    position: relative !important;
                    overflow: hidden !important;
                    transition: all 0.3s ease !important;
                }

                ::v-deep .uni-swiper-dot-active {
                    background: linear-gradient(90deg,
                        rgba(255,255,255,0.3) 0%,
                        rgba(255,255,255,0.6) 30%,
                        rgba(255,255,255,1) 60%,
                        rgba(255,255,255,1) 100%) !important;
                    background-size: 200% 100% !important;
                    animation: loading-progress 3.5s linear infinite !important;
                    width: 80rpx !important;
                    height: 6rpx !important;
                }
        
        @keyframes loading-progress {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }
        
        @keyframes logo-shine {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .banner-item {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            
            .banner-image {
                width: 100%;
                height: 100%;
                transition: transform 0.3s ease;
            }
            
            .banner-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1;
            }
            
            // 图片名称css
            // .banner-content {
            //     position: absolute;
            //     bottom: 250rpx;
            //     left: 30rpx;
            //     right: 30rpx;
            //     color: white;
            //     z-index: 2;
            //     text-align: left;
                
            //     .banner-title {
            //         display: block;
            //         font-size: 40rpx;
            //         font-weight: 800;
            //         letter-spacing: 1rpx;
            //         text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.5);
            //         line-height: 1.3;
            //         margin: 0;
            //     }
            // }
        }
    }
}

// 分类导航样式
.category-nav-wrapper {
    background: #fff;
    padding: 30rpx 0 30rpx;
}

.category-nav {
    width: 100%;
    white-space: nowrap;
    
    .nav-content {
        display: flex;
        align-items: center;
        padding: 0 30rpx;
        gap: 60rpx;
        min-width: fit-content;
    }
    
    .nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        flex-shrink: 0;
        padding-bottom: 10rpx;
        
        &::after {
            content: '';
            position: absolute;
            bottom: 0rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 4rpx;
            background: #000;
            border-radius: 2rpx;
            transition: width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1;
            opacity: 1;
            display: block;
        }
        
        &.active {
            &::after {
                width: 100% !important;
                opacity: 1 !important;
            }
        }
        
        .nav-text {
            font-size: 32rpx;
            color: #666;
            font-weight: 400;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            text-align: center;
            line-height: 1.2;
            white-space: nowrap;
            transform: scale(1);
        }
        
        &.active .nav-text {
            color: #000 !important;
            font-weight: 800 !important;
            transform: scale(1.02);
        }
        
        &:hover:not(.active) .nav-text {
            color: #333;
            transform: scale(1.01);
        }
    }
}

// 隐藏滚动条
.category-nav::-webkit-scrollbar {
    display: none;
}

// 商品推荐区域样式
.product-section {
    margin: 15rpx 30rpx;
    background-color: #fff;
    border-radius: 0;
    padding: 10px 0;
    box-shadow: none;
    position: relative;
    right: 20rpx;
    
    .section-header {
        text-align: left;
        margin-bottom: 30rpx;
        padding: 0 20rpx;
        opacity: 1;
        transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
        
        &.switching {
            opacity: 0;
        }
        
        &.fade-in {
            opacity: 1;
        }
        
        .section-title {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
            color: #000;
            margin-bottom: 0;
            letter-spacing: 0;
            
        }
    }
    
    // 商品特色展示区域 - 长方形横向布局
    .product-showcase {
        margin: -10rpx 0 25rpx 0;
        display: flex;
        gap: 0rpx;
        padding: 0 20rpx;
        opacity: 1;
        transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
        
        &.switching {
            opacity: 0;
        }
        
        &.fade-in {
            opacity: 1;
        }
        
        // 商品大图片 - 长方形设计
        .product-hero {
            position: relative;
            flex: 1;
            height: 430rpx;
            overflow: hidden;
            box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12), 0 2rpx 8rpx rgba(0,0,0,0.08);
            opacity: 1;
            transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
            
            .switching & {
                opacity: 0;
                transition-delay: 0ms;
            }
            
            .fade-in & {
                opacity: 1;
                transition-delay: 0ms;
            }
            
            &:active {
                transform: scale(0.98);
                box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15), 0 1rpx 4rpx rgba(0,0,0,0.1);
            }
            
            .product-hero-image {
                width: 100%;
                height: 100%;
                transition: transform 0.6s ease;
            }
            

            
            .product-hero-content {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 32rpx 28rpx;
                color: white;
                z-index: 2;
                
                .hero-title {
                    display: block;
                    font-size: 36rpx;
                    font-weight: 800;
                    margin-bottom: 10rpx;
                    text-shadow: 0 3rpx 6rpx rgba(0,0,0,0.4);
                    letter-spacing: 0.5rpx;
                }
                
                .hero-subtitle {
                    display: block;
                    font-size: 24rpx;
                    opacity: 0.95;
                    margin-bottom: 8rpx;
                    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.4);
                    font-weight: 500;
                }
                
                .hero-description {
                    display: block;
                    font-size: 22rpx;
                    opacity: 0.9;
                    margin-bottom: 24rpx;
                    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.4);
                    line-height: 1.4;
                }
                
                .hero-cta {
                    .cta-btn {
                        display: inline-block;
                        padding: 12rpx 28rpx;
                        background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
                        border: 1.5rpx solid rgba(255,255,255,0.4);
                        border-radius: 25rpx;
                        font-size: 22rpx;
                        font-weight: 600;
                        color: white;
                        text-align: center;
                        backdrop-filter: blur(12rpx);
                        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                        box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
                        
                        &:active {
                            background: linear-gradient(135deg, rgba(255,255,255,0.35), rgba(255,255,255,0.25));
                            transform: scale(0.96);
                            box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.25);
                        }
                    }
                }
            }
        }
        
        // 商品轮播图 - 长方形设计
        .product-carousel {
            flex: 1;
            opacity: 1;
            transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
            position: relative;
            left: 37rpx;
            
            .switching & {
                opacity: 0;
                transition-delay: 80ms;
            }
            
            .fade-in & {
                opacity: 1;
                transition-delay: 80ms;
            }
            
            .product-swiper {
                width: 100%;
                height: 430rpx;
                overflow: hidden;
                box-shadow: 0 6rpx 24rpx rgba(0,0,0,0.1), 0 2rpx 6rpx rgba(0,0,0,0.06);
                
                :deep(.uni-swiper-dots) {
                    bottom: 20rpx;

                    .uni-swiper-dot {
                        width: 80rpx !important;
                        height: 6rpx !important;
                        border-radius: 3rpx !important;
                        background-color: rgba(255, 255, 255, 0.3) !important;
                        margin: 0 6rpx !important;
                        transition: all 0.3s ease;
                    }

                    .uni-swiper-dot-active {
                        width: 80rpx !important;
                        height: 6rpx !important;
                        background: linear-gradient(90deg,
                            rgba(255, 255, 255, 0.6) 0%,
                            rgba(255, 255, 255, 0.8) 30%,
                            rgba(255, 255, 255, 1) 60%,
                            rgba(255, 255, 255, 1) 100%) !important;
                        background-size: 200% 100% !important;
                        animation: loading-progress 3.5s linear infinite !important;
                    }
                }
                
                .carousel-item {
                    width: 100%;
                    height: 100%;
                    
                    .carousel-image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
    
    .product-scroll {
        width: 100%;
        white-space: nowrap;
        margin-top: -1rpx;    
        margin-left: 5rpx;
        
        .product-list {
            display: flex;
            padding: 0 20rpx;
            gap: 10rpx;
            opacity: 1;
            transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
            
            &.switching {
                opacity: 0;
            }
            
            &.fade-in {
                opacity: 1;
            }
        }
        
        .product-item {
            width: 260rpx;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            opacity: 1;
            transition: opacity 1.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            margin-right: 20rpx;
            
            .switching & {
                opacity: 0;
                
                &:nth-child(1) { transition-delay: 0ms; }
            &:nth-child(2) { transition-delay: 60ms; }
            &:nth-child(3) { transition-delay: 120ms; }
            &:nth-child(4) { transition-delay: 180ms; }
            &:nth-child(5) { transition-delay: 240ms; }
            }
            
            .fade-in & {
                opacity: 1;
                
                &:nth-child(1) { transition-delay: 0ms; }
            &:nth-child(2) { transition-delay: 80ms; }
            &:nth-child(3) { transition-delay: 160ms; }
            &:nth-child(4) { transition-delay: 240ms; }
            &:nth-child(5) { transition-delay: 320ms; }
            }
            
            .product-image-wrapper {
                width: 100%;
                height: 320rpx;
                overflow: hidden;
                position: relative;
                background: #fff;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
                
                .product-image {
                    width: 100%;
                    height: 100%;
                    transition: transform 0.3s ease;
                }
                
                .product-tag {
                    position: absolute;
                    top: 10rpx;
                    left: 10rpx;
                    background: rgba(255, 255, 255, 0.95);
                    color: #666;
                    font-size: 20rpx;
                    padding: 6rpx 10rpx;
                    border-radius: 8rpx;
                    backdrop-filter: blur(10rpx);
                    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
                }
            }
            
            .product-info {
                padding: 16rpx 0 0 0;
                background: transparent;
                margin-top: 8rpx;
                
                .product-name {
                    font-size: 26rpx;
                    font-weight: 500;
                    color: #000;
                    margin-bottom: 8rpx;
                    line-height: 1.4;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                }
                
                .product-description {
                    font-size: 24rpx;
                    color: #666;
                    margin-bottom: 16rpx;
                    line-height: 1.3;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                }
                
                .product-price {
                    display: flex;
                    align-items: baseline;
                    margin-top: -5rpx;
                    
                    .price-symbol {
                        font-size: 26rpx;
                        color: #d3ab73;
                        font-weight: 500;
                        margin-right: 2rpx;
                    }

                    .price-value {
                        font-size: 26rpx;
                        font-weight: 600;
                        color: #d3ab73;
                    }
                }
            }
        }
        
        .more-hint-item {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: transparent;
            margin-top: 50rpx;
            
            .more-hint-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                text-align: center;
                background: transparent;
                padding: 20rpx 0;
                
                .hint-text {
                    font-size: 22rpx;
                    color: #999;
                    font-weight: 400;
                    line-height: 1.8;
                    writing-mode: vertical-lr;
                    text-orientation: upright;
                    letter-spacing: 8rpx;
                }
                
                .hint-arrow {
                    font-size: 40rpx;
                    color: #999;
                    font-weight: 300;
                    opacity: 0.6;
                }
            }
        }
    }
}

// 本季新品推荐标题样式
.season-new-section {
    margin: 40rpx 30rpx 30rpx;
    padding: 20rpx 0;
    position: relative;
    
    .season-new-title {
        font-size: 35rpx;
        font-weight: 800;
        color: #000;
        letter-spacing: 2rpx;
        position: relative;
        display: inline-block;
        
        &::after {
            content: '';
            position: absolute;
            bottom: -8rpx;
            left: 0;
            width: 60%;
            height: 6rpx;
            background: linear-gradient(90deg, #d3ab73 0%, rgba(211, 171, 115, 0.3) 100%);
            border-radius: 3rpx;
        }
    }
}

// 可展开图片轮播区域样式
.expandable-carousel-section {
    margin: 30rpx;
    margin-bottom: 40rpx;
    
    .carousel-container {
        position: relative;
        height: 600rpx;
        border-radius: 0;
        overflow: hidden;
        
        .swipe-area {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .carousel-panels {
            display: flex;
            height: 100%;
            gap: 2rpx;
            
            .panel-item {
                position: relative;
                flex: 0.6;
                transition: all 0.6s ease-out;
                border-radius: 0;
                overflow: hidden;
                opacity: 0.8;
                box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.15),
                            0 -4rpx 12rpx rgba(0, 0, 0, 0.1),
                            0 2rpx 8rpx rgba(0, 0, 0, 0.08);
                
                // 激活状态：占据更大空间
                &.active {
                    flex: 6;
                    opacity: 1;
                    z-index: 10;
                    transform: none;
                    box-shadow: 0 -8rpx 20rpx rgba(0, 0, 0, 0.15),
                                0 -4rpx 10rpx rgba(0, 0, 0, 0.1),
                                0 2rpx 8rpx rgba(0, 0, 0, 0.08);

                    .panel-image {
                        filter: none;
                        transform: none;
                    }
                    
                    .panel-overlay {
                        background: linear-gradient(
                            to bottom,
                            rgba(0, 0, 0, 0) 0%,
                            rgba(0, 0, 0, 0.1) 70%,
                            rgba(0, 0, 0, 0.6) 100%
                        );
                    }
                }
                
                // 非激活状态：轻微缩小但保持清晰
                &:not(.active) {
                    transform: scale(0.98);
                    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1),
                                0 -2rpx 8rpx rgba(0, 0, 0, 0.06),
                                0 1rpx 4rpx rgba(0, 0, 0, 0.04);

                    .panel-image {
                        filter: brightness(0.9) saturate(0.9);
                    }

                    .panel-overlay {
                        background: rgba(0, 0, 0, 0.2);
                    }
                }
                
                .panel-image {
                    width: 100%;
                    height: 100%;
                    transition: all 0.6s ease-out;
                    object-fit: cover;
                }
                
                .panel-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(
                        135deg,
                        rgba(0, 0, 0, 0.4) 0%,
                        rgba(0, 0, 0, 0.2) 40%,
                        rgba(0, 0, 0, 0.6) 100%
                    );
                    transition: all 0.8s ease;
                    z-index: 1;
                }
                
                .panel-content {
                    position: absolute;
                    bottom: 80rpx;
                    left: 60rpx;
                    right: 60rpx;
                    color: white;
                    opacity: 0;
                    transform: translateY(30rpx) scale(0.95);
                    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    z-index: 4;
                    text-align: left;
                    
                    .panel-title {
                        display: block;
                        font-size: 33rpx;
                        font-weight: 800;
                        margin-bottom: 16rpx;
                        text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.8);
                        line-height: 1.1;
                        letter-spacing: 2rpx;
                        color: #ffffff;
                        opacity: 0;
                        transform: translateY(15rpx) scale(0.96);
                        transition: 
                            opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                            transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                        transition-delay: 0.1s;
                    }
                    
                    .panel-subtitle {
                        display: block;
                        font-size: 28rpx;
                        font-weight: 500;
                        opacity: 0;
                        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.6);
                        letter-spacing: 1rpx;
                        color: rgba(255, 255, 255, 0.9);
                        transform: translateY(15rpx) scale(0.96);
                        transition: 
                            opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                            transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                        transition-delay: 0.2s;
                    }
                }
                
                &.active .panel-content {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                    transition-delay: 0.15s;
                    
                    .panel-title {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                        transition-delay: 0.25s;
                    }
                    
                    .panel-subtitle {
                        opacity: 0.9;
                        transform: translateY(0) scale(1);
                        transition-delay: 0.35s;
                    }
                }
                
                // 非激活状态的文字平滑快速淡出
                &:not(.active) .panel-content {
                    opacity: 0;
                    transform: translateY(8rpx) scale(0.98);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
                    transition-delay: 0s;
                    
                    .panel-title {
                        opacity: 0;
                        transform: translateY(5rpx) scale(0.98);
                        transition: 
                            opacity 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                            transform 0.3s cubic-bezier(0.4, 0, 0.6, 1);
                        transition-delay: 0s;
                    }
                    
                    .panel-subtitle {
                        opacity: 0;
                        transform: translateY(5rpx) scale(0.98);
                        transition: 
                            opacity 0.25s cubic-bezier(0.4, 0, 0.6, 1),
                            transform 0.25s cubic-bezier(0.4, 0, 0.6, 1);
                        transition-delay: 0s;
                    }
                }
                

                
                // 移动端点击效果
                &:active {
                    transform: scale(0.98);
                    transition-duration: 0.15s;
                }
                
            }
        }
        
        .carousel-indicators {
            position: absolute;
            bottom: 30rpx;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8rpx;
            z-index: 15;
            
            .indicator {
                width: 30rpx;
                height: 6rpx;
                border-radius: 0;
                background: rgba(255, 255, 255, 0.4);
                transition: all 0.4s ease-out;
                
                &.active {
                    background: rgba(255, 255, 255, 1);
                    width: 40rpx;
                }
                
                &:active {
                    transform: scale(0.9);
                    transition-duration: 0.15s;
                }
            }
        }

    }
}

// 户外经典商品区域样式
.outdoor-classics-section {
    margin: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 0;
    padding: 10px 0;
    box-shadow: none;
    position: relative;
    right: 20rpx;
    
    .section-title-container {
        margin-bottom: 30rpx;
        padding: 0 20rpx;
        
        .section-title {
            font-size: 35rpx;
            font-weight: 800;
            color: #000;
            letter-spacing: 2rpx;
            position: relative;
            display: inline-block;
            
            &::after {
                content: '';
                position: absolute;
                bottom: -8rpx;
                left: 0;
                width: 60%;
                height: 6rpx;
                background: linear-gradient(90deg, #d3ab73 0%, rgba(211, 171, 115, 0.3) 100%);
                border-radius: 3rpx;
            }
        }
    }
    
    .outdoor-product-scroll {
        width: 100%;
        white-space: nowrap;
        margin-top: -1rpx;    
        margin-left: 5rpx;
        
        .outdoor-product-list {
            display: flex;
            padding: 0 20rpx;
            gap: 10rpx;
            
            .outdoor-product-item {
                width: 260rpx;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                margin-right: 20rpx;
                
                .outdoor-product-image-wrapper {
                    width: 100%;
                    height: 320rpx;
                    overflow: hidden;
                    position: relative;
                    background: #fff;
                    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
                    
                    .outdoor-product-image {
                        width: 100%;
                        height: 100%;
                        transition: transform 0.3s ease;
                    }
                }
                
                .outdoor-product-info {
                    padding: 16rpx 0 0 0;
                    background: transparent;
                    
                    .outdoor-product-name {
                        font-size: 24rpx;
                        font-weight: 500;
                        color: #000;
                        margin-bottom: 8rpx;
                        line-height: 1.4;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                    }
                    
                    .outdoor-product-code {
                        font-size: 20rpx;
                        color: #666;
                        margin-bottom: 16rpx;
                        line-height: 1.3;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 1;
                        overflow: hidden;
                    }
                    
                    .outdoor-product-price {
                        display: flex;
                        align-items: baseline;
                        
                        .outdoor-price-symbol {
                            font-size: 25rpx;
                            color: #d3ab73;
                            font-weight: 500;
                            margin-right: 2rpx;
                        }
                        
                        .outdoor-price-value {
                            font-size: 25rpx;
                            font-weight: 600;
                            color: #d3ab73;
                        }
                    }
                }
            }
            
            .outdoor-more-hint-item {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                background: transparent;
                margin-top: 50rpx;
                
                .outdoor-more-hint-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    background: transparent;
                    padding: 20rpx 0;
                    
                    .outdoor-hint-text {
                        font-size: 22rpx;
                        color: #999;
                        font-weight: 400;
                        line-height: 1.8;
                        writing-mode: vertical-lr;
                        text-orientation: upright;
                        letter-spacing: 8rpx;
                    }
                }
            }
        }
    }
}

// SANKUWA 品牌介绍区域样式
.brand-section {
    margin-top: 60rpx;
    position: relative;
    z-index: 2;
    
    .brand-background {
        position: relative;
        height: 800rpx;
        overflow: hidden;
        
        .brand-bg-image {
            width: 100%;
            height: 100%;
        }
        
        .brand-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.4) 0%,
                rgba(0, 0, 0, 0.6) 50%,
                rgba(0, 0, 0, 0.8) 100%
            );
        }
        
        .brand-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            width: 85%;
            
            .brand-logo {
                display: block;
                font-size: 72rpx;
                font-weight: 800;
                letter-spacing: 8rpx;
                margin-bottom: 20rpx;
                text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
            }
            
            .brand-slogan {
                display: block;
                font-size: 32rpx;
                font-weight: 300;
                opacity: 0.9;
                letter-spacing: 4rpx;
                margin-bottom: 60rpx;
                text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
            }
            
            .brand-description {
                display: block;
                font-size: 28rpx;
                line-height: 1.8;
                opacity: 0.9;
                margin-bottom: 80rpx;
                text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
                text-align: justify;
                text-indent: 2em;
            }
            
            .copyright {
                text-align: center;
                
                .copyright-text {
                    display: block;
                    font-size: 24rpx;
                    opacity: 0.7;
                    margin-bottom: 8rpx;
                    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
                }
                
                .icp-text {
                    display: block;
                    font-size: 22rpx;
                    opacity: 0.6;
                    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
                    cursor: pointer;
                    text-decoration: underline;
                    transition: opacity 0.3s ease;

                    &:hover {
                        opacity: 0.8;
                    }

                    &:active {
                        opacity: 1;
                    }
                }
            }
        }
    }
}

// 悬浮活动入口样式
.floating-activity-entrance {
    position: fixed;
    top: 70%;
    right: -80rpx; // 默认隐藏在右侧
    transform: translateY(-50%);
    width: 120rpx;      
    height: 120rpx;
    z-index: 998;
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
    
    .activity-image {
        width: 100%;
        height: 100%;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.15));
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }
    
    // 悬停状态
    &.entrance-hover {
        right: 10rpx; // 显示完整图片
        transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        
        .activity-image {
            transform: scale(1.08) rotate(2deg);
            filter: drop-shadow(0 16rpx 32rpx rgba(0, 0, 0, 0.2)) brightness(1.08) saturate(1.1);
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .activity-glow {
            opacity: 1;
            animation: smoothPulse 2s ease-in-out infinite, smoothRotate 10s linear infinite;
        }
    }
    
    // 点击动画
    &:active {
        transform: translateY(-50%) scale(0.96);
        transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

        .activity-image {
            filter: drop-shadow(0 6rpx 12rpx rgba(0, 0, 0, 0.3)) brightness(1.15);
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }
    }

    // 点击状态动画
    &.entrance-clicked {
        animation: smoothClickBounce 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

        .activity-image {
            animation: smoothClickGlow 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .activity-glow {
            opacity: 1;
            animation: smoothClickPulse 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
    }
}

// 动画效果 - 优化平滑度
@keyframes smoothPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.4;
    }
}

@keyframes smoothRotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

// 点击动画关键帧 - 更平滑的弹跳效果
@keyframes smoothClickBounce {
    0% {
        transform: translateY(-50%) scale(1);
    }
    30% {
        transform: translateY(-50%) scale(0.92);
    }
    60% {
        transform: translateY(-50%) scale(1.08);
    }
    80% {
        transform: translateY(-50%) scale(0.98);
    }
    100% {
        transform: translateY(-50%) scale(1);
    }
}



@keyframes smoothClickPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0;
    }
    30% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.6;
    }
    70% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.2);
        opacity: 0;
    }
}

@keyframes tipFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-10rpx);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

// 添加一个微妙的边缘提示效果
.floating-activity-entrance::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20rpx;
    transform: translateY(-50%);
    width: 8rpx;
    height: 40rpx;
    background: linear-gradient(
        to right,
        transparent 0%,
        rgba(255, 215, 0, 0.8) 50%,
        rgba(255, 165, 0, 0.6) 100%
    );
    border-radius: 8rpx 0 0 8rpx;
    opacity: 0.7;
    animation: smoothShimmer 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    transition: all 0.3s ease;
}

@keyframes smoothShimmer {
    0%, 100% {
        opacity: 0.2;
        transform: translateY(-50%) scaleY(0.3) scaleX(0.8);
    }
    25% {
        opacity: 0.6;
        transform: translateY(-50%) scaleY(0.8) scaleX(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scaleY(1.2) scaleX(1.1);
    }
    75% {
        opacity: 0.6;
        transform: translateY(-50%) scaleY(0.8) scaleX(1);
    }
}

.line-through {
    text-decoration: line-through;
}
.index .dist_s {
    height: 401rpx;
    position: absolute;
    top: -300rpx;
    left: 0;
    width: 100%;
    background-color: #fc4141;
}
.index .dist_base {
    height: 150rpx;
    position: absolute;
    top: 100rpx;
    left: 0;
    width: 100%;
    overflow: hidden;
}
.index .dist_base:after {
    position: absolute;
    left: -10%;
    right: -10%;
    bottom: 0rpx;
    z-index: -1;
    content: "";
    height: 150rpx;
    border-radius: 0 0 100% 100%;
    background-color: #fc4141;
}
.index .index-bg .slide-navigator .slide-image {
    width: 100%;
    height: 100%;
    border-radius: 15rpx;
}
.index .index-bg .wx-swiper-dot {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background-color: rgba(255,255,255,0.3);
    margin: 0;
    position: relative;
    overflow: hidden;
}
.index .index-bg .wx-swiper-dot-active {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0.3) 0%, 
        rgba(255,255,255,0.6) 30%, 
        rgba(255,255,255,1) 60%, 
        rgba(255,255,255,1) 100%);
    background-size: 200% 100%;
    animation: loading-progress 4s linear infinite;
}
.index .index-bg .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: 5rpx;
}
.index .nav {
    padding-top: 26rpx;
}
.index .nav .item {
    width: 25%;
    text-align: center;
    font-size: 26rpx;
    margin-bottom: 35rpx;
}
.index .nav .item .pictrue {
    width: 90rpx;
    height: 90rpx;
    margin: 0 auto 15rpx auto;
}
.index .nav .item .pictrue image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}
.index .news {
    height: 77rpx;
    border-top: 1rpx solid #f4f4f4;
    padding: 0 30rpx;
    box-shadow: 0 10rpx 30rpx #f5f5f5;
}
.index .news .pictrue {
    width: 124rpx;
    height: 28rpx;
    border-right: 1rpx solid #ddd;
    padding-right: 23rpx;
    box-sizing: content-box;
}
.index .news .pictrue image {
    width: 100%;
    height: 100%;
}
.index .news .swiperTxt {
    width: 523rpx;
    height: 100%;
    line-height: 77rpx;
    overflow: hidden;
}
.index .news .swiperTxt .text {
    width: 480rpx;
}
.index .news .swiperTxt .text .label {
    font-size: 20rpx;
    color: #ff4c48;
    width: 64rpx;
    height: 30rpx;
    border-radius: 40rpx;
    text-align: center;
    line-height: 28rpx;
    border: 2rpx solid #ff4947;
}
.index .news .swiperTxt .text .newsTitle {
    width: 397rpx;
    font-size: 24rpx;
    color: #666;
}
.index .news .swiperTxt .iconfont {
    font-size: 28rpx;
    color: #888;
}
.index .news .swiperTxt swiper {
    height: 100%;
}
.index .specialArea {
    padding: 30rpx;
}
.index .specialArea .assemble {
    width: 260rpx;
    height: 260rpx;
    position: relative;
}
.index .specialArea .assemble image {
    width: 100%;
    height: 100%;
    border-radius: 5rpx;
}
.index .specialArea .assemble .text {
    position: absolute;
    top: 37rpx;
    left: 22rpx;
}
.index .specialArea .name {
    font-size: 30rpx;
    color: #fff;
}
.index .specialArea .infor {
    font-size: 22rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5rpx;
}
.index .specialArea .list {
    height: 260rpx;
    width: 416rpx;
}
.index .specialArea .item {
    width: 100%;
    height: 124rpx;
    position: relative;
}
.index .specialArea .item image {
    width: 100%;
    height: 100%;
}
.index .specialArea .item .text {
    position: absolute;
    top: 23rpx;
    left: 28rpx;
}
.index .wrapper .title {
    border-top: 1rpx solid #eee;
    padding-top: 34rpx;
    margin: 0 30rpx;
}
.index .wrapper .title .text {
    font-size: 24rpx;
    color: #999;
    width: 530rpx;
}
.index .wrapper .title .text .name {
    color: #282828;
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 5rpx;
    position: relative;
}
.index .wrapper .title .text .name .new {
    position: absolute;
    top: 2rpx;
    left: 130rpx;
    font-size: 16rpx;
    font-weight: bold;
}
.index .wrapper .title .more {
    font-size: 26rpx;
    color: #333;
}
.index .wrapper .title .more .iconfont {
    margin-left: 9rpx;
    font-size: 26rpx;
    vertical-align: 3rpx;
}
.index .wrapper .scroll-product {
    white-space: nowrap;
    margin-top: 38rpx;
    padding: 0 30rpx 37rpx 30rpx;
}
.index .wrapper .scroll-product .item {
    width: 180rpx;
    display: inline-block;
    margin-right: 19rpx;
    border-bottom: 4rpx solid #47b479;
    box-shadow: 0 40rpx 30rpx -10rpx #eee;
}
.index .wrapper .scroll-product .item:nth-of-type(3n) {
    border-bottom: 4rpx solid #ff6960;
}
.index .wrapper .scroll-product .item:nth-of-type(3n-1) {
    border-bottom: 4rpx solid #579afe;
}
.index .wrapper .scroll-product .item:nth-last-child(1) {
    margin-right: 0;
}
.index .wrapper .scroll-product .item .goods-item {
    width: 100%;
    height: 180rpx;
}
.index .wrapper .scroll-product .item .goods-item image {
    width: 100%;
    height: 100%;
    border-radius: 6rpx 6rpx 0 0;
}
.index .wrapper .scroll-product .item .pro-info {
    font-size: 24rpx;
    color: #282828;
    text-align: center;
    height: 60rpx;
    line-height: 60rpx;
    border: 1rpx solid #f5f5f5;
    border-bottom: 0;
    border-top: 0;
    padding: 0 10rpx;
}
.index .wrapper .boutique {
    width: 690rpx;
    height: 300rpx;
    margin: 28rpx auto 0 auto;
}
.index .wrapper .boutique swiper {
    width: 100%;
    height: 100%;
    position: relative;
}
.index .wrapper .boutique image {
    width: 100%;
    height: 260rpx;
}
.index .wrapper .boutique .wx-swiper-dot {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background-color: rgba(255,255,255,0.3);
    margin: 0;
    position: relative;
    overflow: hidden;
}
.index .wrapper .boutique .wx-swiper-dot-active {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0.3) 0%, 
        rgba(255,255,255,0.6) 30%, 
        rgba(255,255,255,1) 60%, 
        rgba(255,255,255,1) 100%);
    background-size: 200% 100%;
    animation: loading-progress 4s linear infinite;
}
.index .wrapper .boutique .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: -8rpx;
}
.index .hotList .hot-bg {
    width: 100%;
    height: 215rpx;
    background-image: url("data:image/png;base64,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");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    font-size: 24rpx;
    color: #fff;
    margin-top: 15rpx;
}
.index .hotList .hot-bg .title {
    height: 87rpx;
}
.index .hotList .hot-bg .title .text {
    width: 575rpx;
}
.index .hotList .hot-bg .title .text .label {
    font-size: 30rpx;
    font-weight: bold;
    margin-right: 20rpx;
}
.index .hotList .hot-bg .title .more {
    font-size: 26rpx;
}
.index .hotList .hot-bg .title .more .iconfont {
    font-size: 25rpx;
    vertical-align: 2rpx;
    margin-left: 10rpx;
}
.index .hotList .list {
    width: 690rpx;
    height: 330rpx;
    border-radius: 20rpx;
    background-color: #fff;
    margin: -128rpx auto 0 auto;
    padding: 0 22rpx;
    box-sizing: border-box;
    box-shadow: 0 0 30rpx -10rpx #aaa;
}
.index .hotList .list .item {
    width: 200rpx;
}
.index .hotList .list .item ~ .item {
    margin-left: 22rpx;
}
.index .hotList .list .item .pictrue {
    width: 100%;
    height: 200rpx;
    position: relative;
}
.index .hotList .list .item .pictrue image {
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}
.index .hotList .list .item .pictrue .numPic {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    position: absolute;
    top: 7rpx;
    left: 7rpx;
}
.index .hotList .list .item .name {
    font-size: 26rpx;
    color: #282828;
    margin-top: 12rpx;
}
.index .hotList .list .item .money {
    font-size: 20rpx;
    font-weight: bold;
    margin-top: 4rpx;
}
.index .hotList .list .item .money .num {
    font-size: 28rpx;
}
.index .adver {
    width: 100%;
    height: 180rpx;
    margin-top: 37rpx;
}
.index .adver image {
    width: 100%;
    height: 100%;
}
.index .wrapper .newProducts {
    white-space: nowrap;
    padding: 0 30rpx;
    margin: 35rpx 0 42rpx 0;
}
.index .wrapper .newProducts .item {
    display: inline-block;
    width: 240rpx;
    margin-right: 20rpx;
    border: 1rpx solid #eee;
    border-radius: 12rpx;
}
.index .wrapper .newProducts .item:nth-last-child(1) {
    margin-right: 0;
}
.index .wrapper .newProducts .item .goods-item {
    width: 100%;
    height: 240rpx;
}
.index .wrapper .newProducts .item .goods-item image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx 12rpx 0 0;
}
.index .wrapper .newProducts .item .pro-info {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    padding: 19rpx 10rpx 0 10rpx;
}
.index .wrapper .newProducts .item .money {
    padding: 0 10rpx 18rpx 10rpx;
    text-align: center;
    font-size: 26rpx;
    font-weight: bold;
}

.index .overseas_cat_wrapper {
    padding: 0 30rpx;
    margin-top: 20rpx;
}
.index .overseas_cat {
    padding: 30rpx 20rpx 30rpx;
    white-space: nowrap;
    background: #fff;
    border-radius: 15rpx;
}
.index .overseas_cat .flexbox {
    display: flex;
    flex-wrap: nowrap;
}
.index .overseas_cat .item {
    text-align: center;
    font-size: 24rpx;
    height: 100%;
    width: 20%;
    flex-shrink: 0;
}
.index .overseas_cat .item .goods-item {
    padding: 10rpx 0;
}
.index .overseas_cat .item .goods-item image {
    width: 60rpx;
    height: auto;
    height: 60rpx;
}
.index .overseas_cat .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: 5rpx;
    border-radius: 4rpx;
    overflow: hidden;
}
.index .overseas_cat .wx-swiper-dot {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    margin: 0;
    background: rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
}

.index .overseas_cat .wx-swiper-dot-active {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0.3) 0%, 
        rgba(255,255,255,0.6) 30%, 
        rgba(255,255,255,1) 60%, 
        rgba(255,255,255,1) 100%);
    background-size: 200% 100%;
    animation: loading-progress 4s linear infinite;
}

.index .overseas_cat_wrapper1 swiper {
    height: 320rpx;
}
.index .overseas_cat_wrapper1.min_height swiper {
    height: 165rpx;
}
.index .overseas_cat_wrapper2 swiper {
    height: 180rpx;
}
.index .overseas_cat_wrapper2.min_height swiper {
    height: 165rpx;
}
.index .overseas_cat_wrapper.no_page .overseas_cat .wx-swiper-dots.wx-swiper-dots-horizontal {
    display: none;
}

/* 优惠券模块 */
.index .tmcscoupon {
    padding: 0;
    margin: 20rpx 30rpx;
    padding-bottom: 24rpx;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    -webkit-border-radius: 12rpx;
    -moz-border-radius: 12rpx;
    border-radius: 15rpx;
    position: relative;
}
.index .tmcscoupon .coupon-header {
    padding: 24rpx 24rpx 0;
    margin-bottom: 20rpx;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 44rpx;
}
.index .tmcscoupon .coupon-header .coupon-header_title {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.index .tmcscoupon .coupon-header .coupon-header_title .main_title {
    font-size: 32rpx;
    line-height: 32rpx;
    color: #2a3145;
    font-weight: 700;
}
.index .tmcscoupon .coupon-header .coupon-header_title .sub_title {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #aaa;
}
.index .tmcscoupon .coupon-header .coupon-header_more {
    font-size: 26rpx;
    color: #aaa;
}
.index .tmcscoupon .coupon-header .iconfont {
    font-size: 26rpx;
    vertical-align: 3rpx;
}
.index .tmcscoupon swiper {
    height: 140rpx;
}
.index .tmcscoupon .swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    padding: 20rpx 20rpx 0 0;
}
.index .tmcscoupon .swiper-wrapper .flexbox {
    display: flex;
    flex-wrap: nowrap;
}
.index .tmcscoupon .tmcscoupon-item-1 {
    display: flex;
    _width: 50%;
    width: 312rpx;
    overflow: hidden;
    margin-left: 20rpx;
    height: 120rpx;
    overflow: hidden;
    background: #fff;
    -webkit-border-radius: 12rpx;
    -moz-border-radius: 12rpx;
    border-radius: 12rpx;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item {
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 100%;
    display: flex;
    position: relative;
    background: #fff5c5;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 120rpx;
    margin-left: 12rpx;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    min-width: 0;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m:after,
.tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m:before {
    content: "";
    position: absolute;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    width: 16rpx;
    height: 16rpx;
    background: #f7f7f7;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m:before {
    top: -8rpx;
    right: -8rpx;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m:after {
    bottom: -8rpx;
    right: -8rpx;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-info {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    -moz-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    font-weight: 700;
    height: 70rpx;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-info .item_m-info_tag {
    font-size: 28rpx;
    color: #2a3145;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-info .item_m-info_price {
    font-size: 48rpx;
    color: #2a3145;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-info .item_m-info_title {
    font-size: 24rpx;
    margin-left: 8rpx;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 0;
    color: #2a3145;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-rule {
    font-size: 20rpx;
    color: #999;
    margin-top: 6rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_m .tmcscoupon-item_m-date {
    font-size: 20rpx;
    color: #999;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_r {
    width: 64rpx;
    height: 120rpx;
    color: #ff9400;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    font-size: 24rpx;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_r:before {
    content: "";
    height: 80rpx;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAABeAgMAAAB5il18AAAACVBMVEVHcEz/xy7/xi4WmQdzAAAAAnRSTlMAtc2YijsAAAAUSURBVAjXY9BggEARBhgQYRgiYgDQigepHfzaGwAAAABJRU5ErkJggg==);
    width: 4rpx;
    margin-left: -2rpx;
    position: absolute;
    top: 50%;
    left: 0;
    margin-top: -40rpx;
    -webkit-background-size: 100% 100%;
    -moz-background-size: 100% 100%;
    background-size: 100% 100%;
}
.index .tmcscoupon .tmcscoupon-item-1 .tmcscoupon-item .tmcscoupon-item_r .btn {
    width: 120rpx;
    height: 54rpx;
    background: #ff9400;
    -webkit-border-radius: 28rpx;
    -moz-border-radius: 28rpx;
    border-radius: 28rpx;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 28rpx;
    font-weight: 700;
    color: #fff;
}
/* 限时抢购 */
.rush-buy {
    background-color: #fff;
    border-radius: 15rpx;
    margin: 0 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);
}
.rush-buy .rush-head {
    display: flex;
    padding: 20rpx;
    position: relative;
}
.rush-buy .rush-head .head-tit {
    margin-right: 20rpx;
    line-height: 32rpx;
    color: #212121;
    font-size: 28rpx;
    font-weight: 700;
}
.rush-buy .rush-head .head-desc {
    width: 200rpx;
    font-size: 24rpx;
    vertical-align: middle;
    color: #999;
    overflow: hidden;
}
.rush-buy .rush-head .last-wrap {
    position: absolute;
    right: 20rpx;
}
.rush-buy .rush-head .last-wrap .seckill-time-div .cout_time {
    display: inline-block;
    width: 36rpx;
    height: 36rpx;
    line-height: 36rpx;
    border-radius: 10rpx;
    background-color: #333;
    color: #fff;
    font-size: 20rpx;
    text-align: center;
}
.rush-buy .rush-head .last-wrap .seckill-time-div .cout_format {
    display: inline-block;
    padding: 0 6rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 28rpx;
    color: #333;
    font-weight: 700;
}
/* 限时抢购商品样式 */
.goods-box-content {
    background: #fff;
    border-radius: 15rpx;
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
    white-space: nowrap;
}
.goods-box-content .product-list {
    padding: 20rpx 0;
}
.goods-box-content .product-list .product-item {
    display: inline-block;
    width: 30%;
    font-size: 28rpx;
    vertical-align: top;
}
.goods-box-content .product-list .product-item.last {
    width: 66rpx;
    margin: 0;
}
.goods-box-content .product-list .product-item .product-box {
    display: block;
    width: 100%;
}
.goods-box-content .product-list .product-item .product-box .pic-wrap {
    background-color: #fff;
    padding: 16rpx;
}
.goods-box-content .product-list .product-item .product-box .pic-wrap .pic {
    position: relative;
    background-size: 120rpx auto;
}
.goods-box-content .product-list .product-item .product-box .pic-wrap .pic image {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}
.goods-box-content .product-list .product-item .product-box .pic-wrap .pic .tag {
    font-size: 20rpx;
    color: #fff;
    background: #f23030;
    text-align: center;
    line-height: 22rpx;
    border: 1rpx solid #f23030;
    position: absolute;
    left: 0;
    top: -20rpx;
    display: block;
    width: 70rpx;
    height: 24rpx;
    border-radius: 14rpx 4rpx 14rpx 4rpx;
}
.goods-box-content .product-list .product-item .product-box .pic-wrap .pic::after {
    content: "";
    display: block;
    width: 100%;
    padding-top: 100%;
}
.goods-box-content .product-list .product-item .product-box .price-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 20rpx;
}
.goods-box-content .product-list .product-item .product-box .now-price {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    line-height: 24rpx;
    overflow: hidden;
    text-align: center;
}
.goods-box-content .product-list .product-item .product-box .now-price .price {
    font-size: 24rpx;
}
.goods-box-content .product-list .product-item .product-box .market-price {
    font-size: 20rpx;
    color: #999;
}
.goods-box-content .product-list .product-item .product-box .reference {
    text-align: center;
    overflow: hidden;
}
.goods-box-content .product-list .product-item .product-box .reference .jayemart-buy {
    width: 45rpx;
    height: 45rpx;
}
.goods-box-content .product-list .product-item .product-box .reference .jayemart-buy image {
    width: 100%;
    height: 100%;
}
.goods-box-content .product-see-all {
    box-sizing: border-box;
    position: relative;
    width: 66rpx;
    height: 226rpx;
    padding: 20rpx;
    color: #95969f;
    font-size: 24rpx;
    line-height: 40rpx;
}
/* cat_goods2 */
.index .special-mod .special-mod-con {
    padding: 0 30rpx;
    margin-top: 20rpx;
}
.index .special-mod .special-mod-con .section {
    margin: 0 0 15rpx;
    -webkit-box-shadow: 0 10rpx 35rpx -5rpx rgba(0, 0, 0, 0.1);
    box-shadow: 0 10rpx 35rpx -5rpx rgba(0, 0, 0, 0.1);
    border-radius: 15rpx;
    overflow: hidden;
    overflow: hidden;
}
.index .special-mod .special-mod-con .area-conunter-banner {
    width: 100%;
}
.index .special-mod .special-mod-con image {
    width: 100%;
    height: 240rpx;
}
.index .special-mod .special-mod-con .special-mod-container {
    padding: 20rpx 20rpx 0;
    margin-top: -50rpx;
    border-top-left-radius: 30rpx;
    background: #fff;
    overflow: hidden;
    position: relative;
}
.index .special-mod .special-mod-con .special-mod-container swiper {
    height: 400rpx;
}
.index .special-mod .special-mod-con .special-mod-container .pro-flexbox {
    flex-wrap: nowrap;
    display: flex;
}
.index .special-mod .special-mod-con .special-mod-container .pro-item {
    width: 33.33%;
    overflow: hidden;
    font-size: 24rpx;
    height: 100%;
    flex-shrink: 0;
    padding: 10rpx;
    box-sizing: border-box;
}
.index .special-mod .special-mod-con .special-mod-container .pro-item .pro-txt {
    padding: 10rpx 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.index .special-mod .special-mod-con .special-mod-container .pro-item .pro-txt.pro-txt-small {
    font-size: 20rpx;
    color: #999;
    padding: 0;
}
.index .special-mod .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: 5rpx;
    border-radius: 4rpx;
    overflow: hidden;
}
.index .special-mod .wx-swiper-dot {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    margin: 0;
    background: rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
}
.index .special-mod .wx-swiper-dot-active {
    width: 50rpx;
    height: 4rpx;
    border-radius: 0;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0.3) 0%, 
        rgba(255,255,255,0.6) 30%, 
        rgba(255,255,255,1) 60%, 
        rgba(255,255,255,1) 100%);
    background-size: 200% 100%;
    animation: loading-progress 4s linear infinite;
}
.index .special-mod .special-mod-con .cat-default-tit {
    height: 120rpx;
    width: 100%;
    color: #fff;
    position: relative;
}
.index .special-mod .special-mod-con .cat-default-tit .name {
    color: #fff;
    padding: 16rpx 10rpx 0 20rpx;
    display: block;
    margin-left: 20rpx;
    line-height: 40rpx;
    position: relative;
    font-size: 26rpx;
    font-weight: bold;
}
.index .special-mod .special-mod-con .cat-default-tit .name text {
    position: absolute;
    left: 0;
    top: 22rpx;
    width: 6rpx;
    height: 28rpx;
    border-radius: 4rpx;
    background: #fff;
}
.index .special-mod .special-mod-con .cat-default-tit .more {
    position: absolute;
    right: 20rpx;
    top: 16rpx;
    color: #fff;
    font-size: 26rpx;
}
.index .special-mod .special-mod-con .cat-default-tit .more text {
    font-size: 26rpx;
}

.home_ads-1 {
    padding: 10rpx 30rpx 0;
}
.home_ads-1 image {
    overflow: hidden;
    border-radius: 15rpx;
    width: 690rpx;
    height: 230rpx;
    margin-top: 20rpx;
}
/*广告位*/
.home_ads,
.home_rec_goods,
.m-pop-entrance,
.m-special-mod-con {
    margin-left: 30rpx;
    margin-right: 30rpx;
}

.board-header .h1,
.channel-header .h1,
.channel-item .h1,
.life-header .h1,
.promo-header .h1,
.social-header .h1 {
    font-size: 28rpx;
    white-space: nowrap;
}

.home-bar-live {
    display: flex;
}
.home-bar-live .line {
    height: 30rpx;
    border-left: 1rpx solid #ddd;
    position: absolute;
    right: 0;
    top: 15rpx;
}
.home-bar-live .item {
    width: 50%;
    text-align: center;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    position: relative;
    font-weight: bold;
}
.home-bar-live .item-live image {
    width: 50rpx;
    height: 50rpx;
    padding-left: 6rpx;
    vertical-align: middle;
    margin-top: -2rpx;
}
.home-bar-live .item .arrow {
    background: #fc4141;
    height: 5rpx;
    width: 40%;
    position: absolute;
    left: 30%;
    right: 30%;
    border-radius: 6rpx;
    bottom: 0rpx;
}

.live-list-warp {
    padding: 30rpx 30rpx 50rpx 30rpx;
    overflow: hidden;
}
.live-list .live-item {
    background: #fff;
    width: 335rpx;
    border-radius: 10rpx;
    float: left;
    height: 600rpx;
    overflow: hidden;
    position: relative;
    margin-bottom: 20rpx;
}
.live-list .live-item:nth-child(2n) {
    float: right;
}
.live-list .live-item .bg_img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
}
.live-list .live-item .shadow_bg {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0) linear-gradient(rgba(0, 0, 0, 0) 70%, rgba(0, 0, 0, 0.1) 80%, #000000 130%) repeat scroll 0 0;
    z-index: 2;
    position: absolute;
    left: 0;
    top: 0;
}
.live-list .live-item .live-content {
    position: absolute;
    bottom: 0;
    height: 100rpx;
    width: 100%;
    z-index: 3;
    color: #fff;
}
.live-list .live-item .live-content .title {
    font-size: 22rpx;
    padding-left: 15rpx;
    padding-bottom: 10rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 20rpx;
    overflow: hidden;
}
.live-list .live-item .live-content .name {
    font-size: 22rpx;
    padding-left: 15rpx;
}
.live-list .live-item .live-content .anchor_img {
    width: 40rpx;
    height: 40rpx;
    border-radius: 40rpx;
    border: 2rpx solid #fff;
    display: inline-block;
    margin-right: 10rpx;
    vertical-align: middle;
}
.live-list .live-item .live-status {
    position: absolute;
    top: 20rpx;
    left: 10rpx;
    z-index: 3;
}
.live-list .live-item .live-status .status-ico {
    background: #fc4141;
    color: #fff;
    padding: 0 10rpx 0 30rpx;
    height: 35rpx;
    line-height: 35rpx;
    font-size: 22rpx;
    position: relative;
}
.live-list .live-item .live-status .status-ico .status-dot {
    background: #fff;
    height: 10rpx;
    width: 10rpx;
    border-radius: 100%;
    left: 10rpx;
    top: 15rpx;
    position: absolute;
}
.live-list .live-item .live-status .status-ico.status-gray {
    background: #bbb;
}
.live-list .live-item .live-status .status-ico image {
    width: 15rpx;
    height: 15rpx;
    padding-left: 8rpx;
}

/*首页 - 底部推荐商品模块头部*/
.homeTopBar {
    width: 100%;
    height: 120rpx;
    overflow: hidden;
    position: relative;
}
.homeTopBar .title-box {
    display: flex;
    padding: 20rpx;
}
.homeTopBar .title-box.topnavFixed {
    position: fixed;
    width: 100%;
    height: 130rpx;
    top: 265rpx;
    background: #fff;
    z-index: 1;
}
.homeTopBar .title-box.topnavAbsolute {
    position: absolute;
    width: 100%;
    height: 130rpx;
    top: 0 !important;
    background: #fff;
    z-index: 1;
}
.homeTopBar .title-box .title {
    height: 120rpx;
    line-height: 60rpx;
    text-align: center;
    position: relative;
    font-size: 24rpx;
    flex: 1;
}
.homeTopBar .title-box .title .text-fit {
    white-space: nowrap;
}
.homeTopBar .title-box .title:last-child {
    margin-right: 0;
}
.homeTopBar .title-box .title .name {
    color: #333;
}
.homeTopBar .title-box .title.current .name {
    color: #f23030;
    font-size: 120% !important;
    font-weight: 500;
}
.homeTopBar .title-box .title .desc {
    font-size: 20rpx;
    height: 40rpx;
    line-height: 40rpx;
}
.homeTopBar .title-box .title.current .desc {
    background: linear-gradient(to right, #f23030, #f23030);
    color: #fff;
    border-radius: 0 20rpx 20rpx 20rpx;
}
/*首页 - 头条*/
.headlines-box {
    position: relative;
    height: 60rpx;
    margin: 10rpx 30rpx;
    background: #fff;
    border-radius: 18rpx;
}
.headlines-box .headlines-box-wrap {
    display: flex;
}
.headlines-box .headlines-img {
    width: 100%;
    height: 32rpx;
}
.headlines-box .headlines-left {
    width: 160rpx;
    min-width: 160rpx;
    margin-right: 20rpx;
    padding: 10rpx;
    height: 56rpx;
}
.headlines-box .headlines-right {
    width: 90%;
    height: 60rpx;
    line-height: 60rpx;
    overflow: hidden;
}
.headlines-box .headlines-link {
    width: 100%;
    height: 60rpx;
    line-height: 60rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 24rpx;
}

.empty-box {
    height: 500rpx;
    padding-bottom: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

:deep(.uni-swiper-dots) {
    .uni-swiper-dot {
        width: 80rpx !important;
        height: 6rpx !important;
        border-radius: 3rpx !important;
        background-color: rgba(255, 255, 255, 0.3) !important;
        margin: 0 6rpx !important;
        transition: all 0.3s ease !important;

        &.uni-swiper-dot-active {
            background-color: rgba(255, 255, 255, 0.9) !important;
        }
    }
}
</style>
